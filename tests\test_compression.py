"""
Comprehensive tests for the compression utilities.
"""

import os
import tempfile
import unittest
import threading
import time
import random
import string
import sys
from pathlib import Path
from unittest.mock import patch, MagicMock

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.utils.compression import FileCompressor, CompressionMethod, CompressionResult
from src.utils.file_utils import FileUtils


class TestFileCompressor(unittest.TestCase):
    """Comprehensive test cases for FileCompressor class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.compressor = FileCompressor()
        self.temp_dir = Path(tempfile.mkdtemp())
        
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        self.compressor.cleanup()
    
    def _create_test_file(self, filename: str, content: str = None, size_kb: int = None) -> Path:
        """Helper to create test files with specific content or size."""
        test_file = self.temp_dir / filename
        
        if content is not None:
            test_file.write_text(content, encoding='utf-8')
        elif size_kb is not None:
            # Create file of specific size with random content
            content = ''.join(random.choices(string.ascii_letters + string.digits + ' \n', 
                                           k=size_kb * 1024))
            test_file.write_text(content, encoding='utf-8')
        else:
            test_file.write_text("Default test content")
            
        return test_file
    
    def _create_binary_file(self, filename: str, size_kb: int) -> Path:
        """Helper to create binary test files."""
        test_file = self.temp_dir / filename
        content = bytes(random.randint(0, 255) for _ in range(size_kb * 1024))
        test_file.write_bytes(content)
        return test_file

    # Basic Compression Tests
    def test_compress_text_file_gzip(self):
        """Test compressing a text file with GZIP."""
        test_file = self._create_test_file("test.txt", "Hello, World! " * 1000)
        
        result = self.compressor.compress_file(str(test_file), CompressionMethod.GZIP)
        
        self.assertTrue(result.success)
        self.assertEqual(result.method_used, CompressionMethod.GZIP)
        self.assertGreater(result.original_size, 0)
        self.assertLess(result.compressed_size, result.original_size)
        self.assertLess(result.compression_ratio, 1.0)
        self.assertTrue(os.path.exists(result.compressed_file))
        self.assertTrue(result.compressed_file.endswith('.gz'))

    def test_compress_text_file_bzip2(self):
        """Test compressing a text file with BZIP2."""
        test_file = self._create_test_file("test.txt", "Hello, World! " * 1000)
        
        result = self.compressor.compress_file(str(test_file), CompressionMethod.BZIP2)
        
        self.assertTrue(result.success)
        self.assertEqual(result.method_used, CompressionMethod.BZIP2)
        self.assertLess(result.compression_ratio, 1.0)
        self.assertTrue(result.compressed_file.endswith('.bz2'))

    def test_compress_text_file_lzma(self):
        """Test compressing a text file with LZMA."""
        test_file = self._create_test_file("test.txt", "Hello, World! " * 1000)
        
        result = self.compressor.compress_file(str(test_file), CompressionMethod.LZMA)
        
        self.assertTrue(result.success)
        self.assertEqual(result.method_used, CompressionMethod.LZMA)
        self.assertLess(result.compression_ratio, 1.0)
        self.assertTrue(result.compressed_file.endswith('.xz'))

    def test_compress_text_file_zip(self):
        """Test compressing a text file with ZIP."""
        test_file = self._create_test_file("test.txt", "Hello, World! " * 1000)
        
        result = self.compressor.compress_file(str(test_file), CompressionMethod.ZIP)
        
        self.assertTrue(result.success)
        self.assertEqual(result.method_used, CompressionMethod.ZIP)
        self.assertLess(result.compression_ratio, 1.0)
        self.assertTrue(result.compressed_file.endswith('.zip'))

    def test_compress_auto_method_selection(self):
        """Test automatic compression method selection."""
        # Test different file types
        test_cases = [
            ("document.txt", "Text content " * 100, CompressionMethod.LZMA),
            ("script.py", "def hello():\n    print('world')\n" * 50, CompressionMethod.GZIP),
            ("data.json", '{"key": "value", "data": [1,2,3]}' * 100, CompressionMethod.LZMA),
        ]
        
        for filename, content, expected_method in test_cases:
            with self.subTest(filename=filename):
                test_file = self._create_test_file(filename, content)
                result = self.compressor.compress_file(str(test_file), CompressionMethod.AUTO)
                
                self.assertTrue(result.success)
                self.assertEqual(result.method_used, expected_method)

    def test_already_compressed_file(self):
        """Test handling of already compressed files."""
        test_file = self._create_binary_file("test.jpg", 10)
        
        result = self.compressor.compress_file(str(test_file))
        
        # Should not compress
        self.assertTrue(result.success)
        self.assertEqual(result.method_used, CompressionMethod.NONE)
        self.assertEqual(result.compression_ratio, 1.0)
        self.assertEqual(result.compressed_file, str(test_file))

    def test_small_file_no_compression(self):
        """Test that very small files are not compressed."""
        test_file = self._create_test_file("small.txt", "Hi")
        
        result = self.compressor.compress_file(str(test_file))
        
        # Small files should not be compressed
        self.assertTrue(result.success)
        self.assertEqual(result.method_used, CompressionMethod.NONE)

    # Decompression Tests
    def test_decompress_gzip(self):
        """Test decompressing GZIP files."""
        test_file = self._create_test_file("test.txt", "Hello, World! " * 1000)
        original_content = test_file.read_text()
        
        # Compress
        compress_result = self.compressor.compress_file(str(test_file), CompressionMethod.GZIP)
        self.assertTrue(compress_result.success)
        
        # Decompress
        output_file = self.temp_dir / "decompressed.txt"
        success = self.compressor.decompress_file(
            compress_result.compressed_file,
            str(output_file),
            CompressionMethod.GZIP
        )
        
        self.assertTrue(success)
        self.assertTrue(output_file.exists())
        self.assertEqual(output_file.read_text(), original_content)

    def test_decompress_bzip2(self):
        """Test decompressing BZIP2 files."""
        test_file = self._create_test_file("test.txt", "Hello, World! " * 1000)
        original_content = test_file.read_text()
        
        # Compress
        compress_result = self.compressor.compress_file(str(test_file), CompressionMethod.BZIP2)
        self.assertTrue(compress_result.success)
        
        # Decompress
        output_file = self.temp_dir / "decompressed.txt"
        success = self.compressor.decompress_file(
            compress_result.compressed_file,
            str(output_file),
            CompressionMethod.BZIP2
        )
        
        self.assertTrue(success)
        self.assertTrue(output_file.exists())
        self.assertEqual(output_file.read_text(), original_content)

    def test_decompress_lzma(self):
        """Test decompressing LZMA files."""
        test_file = self._create_test_file("test.txt", "Hello, World! " * 1000)
        original_content = test_file.read_text()
        
        # Compress
        compress_result = self.compressor.compress_file(str(test_file), CompressionMethod.LZMA)
        self.assertTrue(compress_result.success)
        
        # Decompress
        output_file = self.temp_dir / "decompressed.txt"
        success = self.compressor.decompress_file(
            compress_result.compressed_file,
            str(output_file),
            CompressionMethod.LZMA
        )
        
        self.assertTrue(success)
        self.assertTrue(output_file.exists())
        self.assertEqual(output_file.read_text(), original_content)

    def test_decompress_zip(self):
        """Test decompressing ZIP files."""
        test_file = self._create_test_file("test.txt", "Hello, World! " * 1000)
        original_content = test_file.read_text()
        
        # Compress
        compress_result = self.compressor.compress_file(str(test_file), CompressionMethod.ZIP)
        self.assertTrue(compress_result.success)
        
        # Decompress
        output_file = self.temp_dir / "decompressed.txt"
        success = self.compressor.decompress_file(
            compress_result.compressed_file,
            str(output_file),
            CompressionMethod.ZIP
        )
        
        self.assertTrue(success)
        self.assertTrue(output_file.exists())
        self.assertEqual(output_file.read_text(), original_content)

    def test_decompress_none_method(self):
        """Test decompressing with NONE method (copy)."""
        test_file = self._create_test_file("test.txt", "Hello, World!")
        original_content = test_file.read_text()
        
        output_file = self.temp_dir / "copied.txt"
        success = self.compressor.decompress_file(
            str(test_file),
            str(output_file),
            CompressionMethod.NONE
        )
        
        self.assertTrue(success)
        self.assertTrue(output_file.exists())
        self.assertEqual(output_file.read_text(), original_content)


    # Edge Cases and Error Handling Tests
    def test_compress_nonexistent_file(self):
        """Test compressing a non-existent file."""
        result = self.compressor.compress_file("/nonexistent/file.txt")

        self.assertFalse(result.success)
        self.assertIsNotNone(result.error_message)
        self.assertIn("File not found", result.error_message)

    def test_compress_empty_file(self):
        """Test compressing an empty file."""
        test_file = self._create_test_file("empty.txt", "")

        result = self.compressor.compress_file(str(test_file))

        # Empty files should not be compressed
        self.assertTrue(result.success)
        self.assertEqual(result.method_used, CompressionMethod.NONE)
        self.assertEqual(result.original_size, 0)

    def test_compress_large_file(self):
        """Test compressing a large file."""
        # Create a 1MB file
        test_file = self._create_test_file("large.txt", size_kb=1024)

        result = self.compressor.compress_file(str(test_file), CompressionMethod.GZIP)

        self.assertTrue(result.success)
        self.assertGreater(result.original_size, 1024 * 1024)  # > 1MB
        self.assertLess(result.compressed_size, result.original_size)

    def test_compress_binary_file(self):
        """Test compressing binary files."""
        test_file = self._create_binary_file("binary.dat", 50)

        result = self.compressor.compress_file(str(test_file))

        self.assertTrue(result.success)
        # Binary files may or may not compress well

    def test_decompress_invalid_file(self):
        """Test decompressing an invalid compressed file."""
        # Create a fake compressed file
        fake_compressed = self.temp_dir / "fake.gz"
        fake_compressed.write_text("This is not a valid gzip file")

        output_file = self.temp_dir / "output.txt"
        success = self.compressor.decompress_file(
            str(fake_compressed),
            str(output_file),
            CompressionMethod.GZIP
        )

        self.assertFalse(success)

    def test_decompress_to_readonly_directory(self):
        """Test decompressing to a read-only directory."""
        test_file = self._create_test_file("test.txt", "Hello, World! " * 100)

        # Compress
        compress_result = self.compressor.compress_file(str(test_file), CompressionMethod.GZIP)
        self.assertTrue(compress_result.success)

        # Try to decompress to non-existent directory
        output_file = "/nonexistent/directory/output.txt"
        success = self.compressor.decompress_file(
            compress_result.compressed_file,
            output_file,
            CompressionMethod.GZIP
        )

        # Should create directory and succeed
        self.assertTrue(success)

    def test_compression_ratio_calculation(self):
        """Test compression ratio calculations."""
        # Highly compressible content
        test_file = self._create_test_file("repetitive.txt", "A" * 10000)

        result = self.compressor.compress_file(str(test_file), CompressionMethod.GZIP)

        self.assertTrue(result.success)
        self.assertLess(result.compression_ratio, 0.1)  # Should compress to < 10%
        self.assertGreater(result.size_reduction_percent, 90)  # > 90% reduction

    def test_should_compress_logic(self):
        """Test the should_compress decision logic."""
        # Test various file types
        test_cases = [
            ("document.txt", True),
            ("image.jpg", False),
            ("video.mp4", False),
            ("archive.zip", False),
            ("code.py", True),
            ("data.json", True),
            ("binary.exe", True),  # Unknown extension, should try
        ]

        for filename, should_compress in test_cases:
            with self.subTest(filename=filename):
                test_file = self._create_test_file(filename, "Test content " * 100)
                result = self.compressor.should_compress(str(test_file))
                self.assertEqual(result, should_compress)

    def test_compression_method_selection_by_size(self):
        """Test compression method selection based on file size."""
        # Large file should use GZIP for speed
        large_file = self._create_test_file("large.txt", size_kb=200)  # 200KB
        method = self.compressor._choose_best_method(str(large_file))
        # For text files, should still use LZMA even if large
        self.assertEqual(method, CompressionMethod.LZMA)

    def test_concurrent_compression(self):
        """Test concurrent compression operations."""
        def compress_file(filename, content):
            test_file = self._create_test_file(filename, content)
            result = self.compressor.compress_file(str(test_file))
            return result.success

        # Create multiple threads
        threads = []
        results = []

        for i in range(5):
            content = f"File {i} content " * 1000
            thread = threading.Thread(
                target=lambda i=i, content=content: results.append(
                    compress_file(f"file_{i}.txt", content)
                )
            )
            threads.append(thread)
            thread.start()

        # Wait for all threads
        for thread in threads:
            thread.join()

        # All compressions should succeed
        self.assertEqual(len(results), 5)
        self.assertTrue(all(results))

    def test_cleanup_functionality(self):
        """Test cleanup of temporary files."""
        test_file = self._create_test_file("test.txt", "Hello, World! " * 1000)

        # Compress file
        result = self.compressor.compress_file(str(test_file), CompressionMethod.GZIP)
        self.assertTrue(result.success)

        # Compressed file should exist
        self.assertTrue(os.path.exists(result.compressed_file))

        # Cleanup
        self.compressor.cleanup()

        # Compressed file should be cleaned up
        # Note: cleanup() removes the temp directory, so file won't exist

    def test_compression_statistics(self):
        """Test compression statistics and metrics."""
        test_file = self._create_test_file("test.txt", "Hello, World! " * 1000)

        result = self.compressor.compress_file(str(test_file), CompressionMethod.GZIP)

        self.assertTrue(result.success)
        self.assertIsInstance(result.original_size, int)
        self.assertIsInstance(result.compressed_size, int)
        self.assertIsInstance(result.compression_ratio, float)
        self.assertIsInstance(result.size_reduction_percent, float)

        # Verify calculations
        expected_ratio = result.compressed_size / result.original_size
        self.assertAlmostEqual(result.compression_ratio, expected_ratio, places=3)

        expected_reduction = (1 - result.compression_ratio) * 100
        self.assertAlmostEqual(result.size_reduction_percent, expected_reduction, places=1)


class TestCompressionIntegration(unittest.TestCase):
    """Integration tests for compression with file transfer system."""

    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())

    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_compression_with_file_utils(self):
        """Test compression integration with FileUtils."""
        # Create test file
        test_file = self.temp_dir / "test.txt"
        test_content = "Hello, World! " * 1000
        test_file.write_text(test_content)

        # Test file size calculation
        original_size = FileUtils.get_file_size(str(test_file))
        self.assertGreater(original_size, 0)

        # Compress file
        compressor = FileCompressor()
        result = compressor.compress_file(str(test_file))

        self.assertTrue(result.success)
        self.assertEqual(result.original_size, original_size)

        # Test compressed file size
        compressed_size = FileUtils.get_file_size(result.compressed_file)
        self.assertEqual(result.compressed_size, compressed_size)

        compressor.cleanup()

    def test_compression_result_serialization(self):
        """Test serialization of compression results."""
        # Create a compression result
        result = CompressionResult(
            original_size=1000,
            compressed_size=500,
            compression_ratio=0.5,
            method_used=CompressionMethod.GZIP,
            compressed_file="/path/to/file.gz",
            success=True
        )

        # Test to_dict
        data = result.to_dict()
        self.assertIsInstance(data, dict)
        self.assertEqual(data['original_size'], 1000)
        self.assertEqual(data['method_used'], 'gzip')

        # Test from_dict
        result2 = CompressionResult.from_dict(data)
        self.assertEqual(result2.original_size, result.original_size)
        self.assertEqual(result2.method_used, result.method_used)


if __name__ == '__main__':
    unittest.main()

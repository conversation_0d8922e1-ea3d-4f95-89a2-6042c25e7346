"""
Integration tests for compression with the file transfer system.
"""

import os
import tempfile
import threading
import time
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.core.server import FileTransferServer
from src.core.enhanced_client import EnhancedFileTransferClient
from src.utils.compression import CompressionMethod
from src.utils.network_utils import NetworkUtils
from src.utils.file_utils import FileUtils
import pytest


class TestCompressionIntegration:
    """Integration tests for compression with file transfer."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for testing."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        
        # Cleanup
        import shutil
        try:
            shutil.rmtree(temp_dir)
        except:
            pass
    
    @pytest.fixture
    def test_files(self, temp_dir):
        """Create test files for transfer."""
        files = []
        
        # Text file that compresses well
        text_file = Path(temp_dir) / "test.txt"
        text_content = "This is a test file with repeating content. " * 1000
        text_file.write_text(text_content)
        files.append(str(text_file))
        
        # JSON file
        json_file = Path(temp_dir) / "data.json"
        json_content = '{"key": "value", "data": [1, 2, 3, 4, 5]}' * 500
        json_file.write_text(json_content)
        files.append(str(json_file))
        
        # Binary file (doesn't compress as well)
        binary_file = Path(temp_dir) / "binary.dat"
        binary_content = bytes(range(256)) * 100  # 25.6KB of binary data
        binary_file.write_bytes(binary_content)
        files.append(str(binary_file))
        
        return files

    def test_file_transfer_with_compression(self, temp_dir, test_files):
        """Test complete file transfer workflow with compression enabled."""
        # Find available port
        port = NetworkUtils.find_available_port(start_port=9000)
        assert port is not None
        
        # Setup receive directory
        receive_dir = Path(temp_dir) / "received"
        receive_dir.mkdir()
        
        # Create server with compression enabled
        server = FileTransferServer(
            host="localhost",
            port=port,
            download_dir=str(receive_dir),
            chunk_size=4096,
            enable_compression=True
        )
        
        # Track received files
        received_files = []
        transfer_stats = []
        
        def on_file_received(filename, client_id):
            received_files.append(filename)
        
        def on_transfer_progress(filename, progress, bytes_received, total_bytes, client_id):
            if progress == 100.0:  # Transfer complete
                transfer_stats.append({
                    'filename': filename,
                    'bytes_received': bytes_received,
                    'total_bytes': total_bytes
                })
        
        server.on_file_received = on_file_received
        server.on_transfer_progress = on_transfer_progress
        
        # Start server in background
        server_thread = threading.Thread(target=server.start, daemon=True)
        server_thread.start()
        time.sleep(0.5)  # Wait for server to start
        
        try:
            # Create client with compression enabled
            client = EnhancedFileTransferClient(
                chunk_size=4096,
                enable_compression=True,
                compression_method=CompressionMethod.AUTO
            )
            
            # Connect to server
            assert client.connect("localhost", port)
            
            # Send all test files
            for file_path in test_files:
                success = client.send_file(file_path)
                assert success, f"Failed to send {file_path}"
            
            # Wait for transfers to complete
            time.sleep(2.0)
            
            # Verify all files were received
            assert len(received_files) == len(test_files)
            
            # Verify file contents
            for original_file in test_files:
                filename = Path(original_file).name
                received_file = receive_dir / filename
                
                assert received_file.exists(), f"File {filename} not received"
                
                # Compare file contents
                if filename.endswith('.dat'):
                    # Binary comparison
                    with open(original_file, 'rb') as f1, open(received_file, 'rb') as f2:
                        assert f1.read() == f2.read(), f"Binary file {filename} content mismatch"
                else:
                    # Text comparison
                    with open(original_file, 'r') as f1, open(received_file, 'r') as f2:
                        assert f1.read() == f2.read(), f"Text file {filename} content mismatch"
            
            # Verify compression was beneficial for text files
            for stat in transfer_stats:
                if stat['filename'].endswith('.txt') or stat['filename'].endswith('.json'):
                    # For highly compressible files, transferred bytes should be less than original
                    original_size = FileUtils.get_file_size(
                        next(f for f in test_files if Path(f).name == stat['filename'])
                    )
                    # Allow some overhead for protocol, but should still see compression benefit
                    assert stat['bytes_received'] < original_size * 0.8, \
                        f"Expected compression benefit for {stat['filename']}"
            
            client.disconnect()
            
        finally:
            server.stop()

    def test_compression_method_selection(self, temp_dir):
        """Test that different compression methods are selected appropriately."""
        # Find available port
        port = NetworkUtils.find_available_port(start_port=9001)
        assert port is not None
        
        # Setup receive directory
        receive_dir = Path(temp_dir) / "received"
        receive_dir.mkdir()
        
        # Create different types of test files
        test_cases = [
            ("document.txt", "Text document content. " * 1000, CompressionMethod.LZMA),
            ("script.py", "def function():\n    return 'value'\n" * 500, CompressionMethod.GZIP),
            ("data.json", '{"key": "value"}' * 1000, CompressionMethod.LZMA),
        ]
        
        for filename, content, expected_method in test_cases:
            # Create test file
            test_file = Path(temp_dir) / filename
            test_file.write_text(content)
            
            # Create server
            server = FileTransferServer(
                host="localhost",
                port=port,
                download_dir=str(receive_dir),
                chunk_size=4096,
                enable_compression=True
            )
            
            # Start server
            server_thread = threading.Thread(target=server.start, daemon=True)
            server_thread.start()
            time.sleep(0.5)
            
            try:
                # Create client with AUTO compression
                client = EnhancedFileTransferClient(
                    chunk_size=4096,
                    enable_compression=True,
                    compression_method=CompressionMethod.AUTO
                )
                
                # Connect and send file
                assert client.connect("localhost", port)
                success = client.send_file(str(test_file))
                assert success
                
                time.sleep(1.0)  # Wait for transfer
                
                # Verify file was received correctly
                received_file = receive_dir / filename
                assert received_file.exists()
                assert received_file.read_text() == content
                
                client.disconnect()
                
            finally:
                server.stop()
                time.sleep(0.2)  # Brief pause between tests

    def test_compression_disabled(self, temp_dir, test_files):
        """Test file transfer with compression disabled."""
        # Find available port
        port = NetworkUtils.find_available_port(start_port=9002)
        assert port is not None
        
        # Setup receive directory
        receive_dir = Path(temp_dir) / "received"
        receive_dir.mkdir()
        
        # Create server with compression disabled
        server = FileTransferServer(
            host="localhost",
            port=port,
            download_dir=str(receive_dir),
            chunk_size=4096,
            enable_compression=False
        )
        
        received_files = []
        
        def on_file_received(filename, client_id):
            received_files.append(filename)
        
        server.on_file_received = on_file_received
        
        # Start server
        server_thread = threading.Thread(target=server.start, daemon=True)
        server_thread.start()
        time.sleep(0.5)
        
        try:
            # Create client with compression disabled
            client = EnhancedFileTransferClient(
                chunk_size=4096,
                enable_compression=False
            )
            
            # Connect and send files
            assert client.connect("localhost", port)
            
            for file_path in test_files:
                success = client.send_file(file_path)
                assert success
            
            time.sleep(2.0)
            
            # Verify all files received correctly
            assert len(received_files) == len(test_files)
            
            for original_file in test_files:
                filename = Path(original_file).name
                received_file = receive_dir / filename
                assert received_file.exists()
                
                # Verify content matches
                original_size = FileUtils.get_file_size(original_file)
                received_size = FileUtils.get_file_size(str(received_file))
                assert original_size == received_size
            
            client.disconnect()
            
        finally:
            server.stop()

    def test_mixed_compression_settings(self, temp_dir):
        """Test behavior when client and server have different compression settings."""
        # Find available port
        port = NetworkUtils.find_available_port(start_port=9003)
        assert port is not None
        
        # Create test file
        test_file = Path(temp_dir) / "test.txt"
        test_content = "Test content for mixed compression settings. " * 500
        test_file.write_text(test_content)
        
        # Setup receive directory
        receive_dir = Path(temp_dir) / "received"
        receive_dir.mkdir()
        
        # Test case: Server has compression enabled, client disabled
        server = FileTransferServer(
            host="localhost",
            port=port,
            download_dir=str(receive_dir),
            chunk_size=4096,
            enable_compression=True
        )
        
        received_files = []
        
        def on_file_received(filename, client_id):
            received_files.append(filename)
        
        server.on_file_received = on_file_received
        
        # Start server
        server_thread = threading.Thread(target=server.start, daemon=True)
        server_thread.start()
        time.sleep(0.5)
        
        try:
            # Client with compression disabled
            client = EnhancedFileTransferClient(
                chunk_size=4096,
                enable_compression=False
            )
            
            assert client.connect("localhost", port)
            success = client.send_file(str(test_file))
            assert success
            
            time.sleep(1.0)
            
            # File should still be received correctly
            assert len(received_files) == 1
            received_file = receive_dir / "test.txt"
            assert received_file.exists()
            assert received_file.read_text() == test_content
            
            client.disconnect()
            
        finally:
            server.stop()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

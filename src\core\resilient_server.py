"""
Interrupt-resilient file transfer server with resume capabilities.
"""

import socket
import threading
import json
import hashlib
import time
from pathlib import Path
from typing import Optional, Callable, Dict, Any, List, Tuple
from src.utils.logger import get_logger
from src.utils.transfer_state import TransferStateManager
from src.utils.network_monitor import NetworkMonitor


class ResilientFileTransferServer:
    """
    Enhanced file transfer server with interrupt resilience and resume capabilities.
    """
    
    def __init__(
        self,
        host: str = "localhost",
        port: int = 8888,
        download_dir: Optional[str] = None,
        chunk_size: int = 65536,  # Increased from 8192 to 64KB for better performance
        max_connections: int = 5,
        state_dir: Optional[str] = None
    ):
        """
        Initialize the resilient file transfer server.
        
        Args:
            host: Server host address
            port: Server port number
            download_dir: Directory to save received files
            chunk_size: Size of data chunks for transfer
            max_connections: Maximum concurrent connections
            state_dir: Directory to store transfer state files
        """
        self.host = host
        self.port = port
        self.chunk_size = chunk_size
        self.max_connections = max_connections
        self.logger = get_logger()
        
        # Initialize transfer state manager
        self.state_manager = TransferStateManager(state_dir)
        self.state_manager.load_existing_transfers()
        
        # Set default download directory
        if download_dir is None:
            self.download_dir = Path.home() / "Downloads" / "FileTransfer"
        else:
            self.download_dir = Path(download_dir)
        
        # Create download directory if it doesn't exist
        self.download_dir.mkdir(parents=True, exist_ok=True)
        
        # Server state
        self.socket = None
        self.running = False
        self.active_connections = {}
        self.active_transfers = {}  # client_id -> transfer_id mapping
        
        # Callbacks for GUI integration
        self.on_client_connected: Optional[Callable] = None
        self.on_client_disconnected: Optional[Callable] = None
        self.on_file_received: Optional[Callable] = None
        self.on_transfer_progress: Optional[Callable] = None
        self.on_transfer_resumed: Optional[Callable] = None
    
    def start(self):
        """Start the server and listen for connections."""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.socket.bind((self.host, self.port))
            self.socket.listen(self.max_connections)
            
            self.running = True
            self.logger.info(f"Resilient server started on {self.host}:{self.port}")
            self.logger.info(f"Download directory: {self.download_dir}")
            
            while self.running:
                try:
                    client_socket, client_address = self.socket.accept()
                    client_id = f"{client_address[0]}:{client_address[1]}_{int(time.time())}"
                    
                    self.logger.info(f"New connection from {client_address} (ID: {client_id})")
                    
                    # Store active connection
                    self.active_connections[client_id] = {
                        'socket': client_socket,
                        'address': client_address,
                        'connected_at': time.time()
                    }
                    
                    # Handle client in separate thread
                    client_thread = threading.Thread(
                        target=self._handle_client,
                        args=(client_socket, client_address, client_id),
                        daemon=True
                    )
                    client_thread.start()
                    
                    if self.on_client_connected:
                        self.on_client_connected(client_id, client_address)
                    
                except socket.error as e:
                    if self.running:
                        self.logger.error(f"Socket error: {e}")
                    break
                except Exception as e:
                    self.logger.error(f"Error accepting connection: {e}")
                    
        except Exception as e:
            self.logger.error(f"Error starting server: {e}")
        finally:
            self.running = False
    
    def stop(self):
        """Stop the server."""
        self.running = False
        
        # Close all active connections
        for client_id, conn_info in self.active_connections.items():
            try:
                conn_info['socket'].close()
            except:
                pass
        
        self.active_connections.clear()
        
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
        
        self.logger.info("Server stopped")
    
    def get_active_transfers(self) -> List[Dict[str, Any]]:
        """Get list of active transfers."""
        transfers = []
        
        for transfer_id, state in self.state_manager.active_transfers.items():
            if not state.completed:
                progress = self.state_manager.get_progress(transfer_id)
                transfers.append({
                    'transfer_id': transfer_id,
                    'file_name': state.file_name,
                    'file_size': state.file_size,
                    'progress': progress,
                    'created': state.created_timestamp,
                    'last_updated': state.last_updated
                })
        
        return transfers
    
    def _handle_client(self, client_socket: socket.socket, client_address: tuple, client_id: str):
        """Handle a client connection."""
        try:
            while self.running:
                # Receive metadata
                metadata = self._receive_metadata(client_socket)
                if not metadata:
                    break
                
                # Handle the file transfer
                success = self._handle_file_transfer(client_socket, client_id, metadata)
                
                # Send response
                response = {"status": "success" if success else "error"}
                self._send_response(client_socket, response)
                
                if not success:
                    break
                    
        except Exception as e:
            self.logger.error(f"Error handling client {client_id}: {e}")
        finally:
            self._cleanup_client(client_id)
    
    def _handle_file_transfer(self, client_socket: socket.socket, client_id: str, metadata: Dict[str, Any]) -> bool:
        """Handle file transfer with resume support."""
        try:
            file_name = metadata['filename']
            file_size = metadata['size']
            file_checksum = metadata['checksum']
            transfer_id = metadata.get('transfer_id')
            is_resume = metadata.get('resume', False)
            
            # Create or get transfer state
            if is_resume and transfer_id:
                self.logger.info(f"Resuming transfer {transfer_id} for {file_name}")
                if self.on_transfer_resumed:
                    self.on_transfer_resumed(transfer_id, client_id)
            else:
                transfer_id = self.state_manager.create_transfer_state(
                    file_name=file_name,
                    file_size=file_size,
                    total_checksum=file_checksum,
                    download_path=str(self.download_dir)
                )
                self.logger.info(f"Starting new transfer {transfer_id} for {file_name}")
            
            # Track active transfer
            self.active_transfers[client_id] = transfer_id
            
            # Prepare file path
            file_path = self.download_dir / file_name
            
            # Handle resume vs new transfer
            if is_resume:
                return self._handle_resume_transfer(client_socket, client_id, transfer_id, file_path, metadata)
            else:
                return self._handle_new_transfer(client_socket, client_id, transfer_id, file_path, file_size)
                
        except Exception as e:
            self.logger.error(f"Error in file transfer: {e}")
            return False
    
    def _handle_new_transfer(
        self, 
        client_socket: socket.socket, 
        client_id: str, 
        transfer_id: str, 
        file_path: Path, 
        file_size: int
    ) -> bool:
        """Handle a new file transfer."""
        try:
            received_bytes = 0
            
            with open(file_path, 'wb') as f:
                while received_bytes < file_size:
                    # Calculate chunk size
                    remaining = file_size - received_bytes
                    chunk_size = min(self.chunk_size, remaining)
                    
                    # Receive chunk
                    chunk = client_socket.recv(chunk_size)
                    if not chunk:
                        break
                    
                    # Write chunk to file
                    f.write(chunk)
                    
                    # Update transfer state
                    chunk_start = received_bytes
                    chunk_end = received_bytes + len(chunk) - 1
                    self.state_manager.add_segment(transfer_id, chunk_start, chunk_end, chunk)
                    
                    received_bytes += len(chunk)
                    
                    # Report progress
                    if self.on_transfer_progress:
                        progress = (received_bytes / file_size) * 100
                        self.on_transfer_progress(file_path.name, progress, received_bytes, file_size, client_id)
            
            # Verify transfer completion
            if received_bytes == file_size:
                self.state_manager.mark_completed(transfer_id)
                
                if self.on_file_received:
                    self.on_file_received(file_path.name, client_id)
                
                self.logger.info(f"File transfer completed: {file_path.name}")
                return True
            else:
                self.logger.error(f"Incomplete transfer: {received_bytes}/{file_size} bytes")
                return False
                
        except Exception as e:
            self.logger.error(f"Error in new transfer: {e}")
            return False
    
    def _handle_resume_transfer(
        self, 
        client_socket: socket.socket, 
        client_id: str, 
        transfer_id: str, 
        file_path: Path, 
        metadata: Dict[str, Any]
    ) -> bool:
        """Handle a resumed file transfer."""
        try:
            missing_ranges = metadata.get('missing_ranges', [])
            state = self.state_manager.active_transfers[transfer_id]
            
            # Open file in append mode for resume
            with open(file_path, 'r+b') as f:
                for start_byte, end_byte in missing_ranges:
                    # Seek to the correct position
                    f.seek(start_byte)
                    
                    # Receive the missing chunk
                    chunk_size = end_byte - start_byte + 1
                    received_data = b""
                    
                    while len(received_data) < chunk_size:
                        remaining = chunk_size - len(received_data)
                        chunk = client_socket.recv(min(self.chunk_size, remaining))
                        if not chunk:
                            break
                        received_data += chunk
                    
                    # Write the chunk
                    f.write(received_data)
                    
                    # Update transfer state
                    self.state_manager.add_segment(transfer_id, start_byte, end_byte, received_data)
                    
                    # Report progress
                    if self.on_transfer_progress:
                        progress = self.state_manager.get_progress(transfer_id)
                        total_received = sum(seg.end_byte - seg.start_byte + 1 for seg in state.segments)
                        self.on_transfer_progress(file_path.name, progress, total_received, state.file_size, client_id)
            
            # Check if transfer is now complete
            missing_ranges = self.state_manager.get_missing_ranges(transfer_id)
            if not missing_ranges:
                self.state_manager.mark_completed(transfer_id)
                
                if self.on_file_received:
                    self.on_file_received(file_path.name, client_id)
                
                self.logger.info(f"Resumed file transfer completed: {file_path.name}")
                return True
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error in resume transfer: {e}")
            return False
    
    def _receive_metadata(self, client_socket: socket.socket) -> Optional[Dict[str, Any]]:
        """Receive metadata from client."""
        try:
            # Receive metadata length
            length_data = client_socket.recv(4)
            if len(length_data) != 4:
                return None
            
            metadata_length = int.from_bytes(length_data, byteorder='big')
            
            # Receive metadata
            metadata_data = b""
            while len(metadata_data) < metadata_length:
                chunk = client_socket.recv(metadata_length - len(metadata_data))
                if not chunk:
                    return None
                metadata_data += chunk
            
            return json.loads(metadata_data.decode('utf-8'))
            
        except Exception as e:
            self.logger.error(f"Error receiving metadata: {e}")
            return None
    
    def _send_response(self, client_socket: socket.socket, response: Dict[str, Any]) -> bool:
        """Send response to client."""
        try:
            response_data = json.dumps(response).encode('utf-8')
            response_length = len(response_data)
            
            # Send response length then response
            client_socket.send(response_length.to_bytes(4, byteorder='big'))
            client_socket.send(response_data)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending response: {e}")
            return False
    
    def _cleanup_client(self, client_id: str):
        """Clean up client connection."""
        # Remove from active connections
        if client_id in self.active_connections:
            try:
                self.active_connections[client_id]['socket'].close()
            except:
                pass
            del self.active_connections[client_id]
        
        # Remove from active transfers
        if client_id in self.active_transfers:
            del self.active_transfers[client_id]
        
        if self.on_client_disconnected:
            self.on_client_disconnected(client_id)
        
        self.logger.info(f"Cleaned up client {client_id}")
    
    def is_running(self) -> bool:
        """Check if server is running."""
        return self.running
    
    def get_connection_count(self) -> int:
        """Get number of active connections."""
        return len(self.active_connections)

#!/usr/bin/env python3
"""
Comprehensive demo showcasing the file compression features.
"""

import sys
import time
import tempfile
import threading
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.server import FileTransferServer
from src.core.enhanced_client import EnhancedFileTransferClient
from src.utils.network_utils import NetworkUtils
from src.utils.file_utils import FileUtils
from src.utils.compression import FileCompressor, CompressionMethod
from src.utils.logger import setup_logger


def create_test_files_for_compression(temp_dir: Path) -> list:
    """Create test files that demonstrate compression benefits."""
    test_files = []
    
    # 1. Large text file (compresses very well)
    large_text = temp_dir / "large_document.txt"
    with open(large_text, 'w', encoding='utf-8') as f:
        f.write("📄 File Transfer Pro - Compression Demo\n")
        f.write("=" * 50 + "\n\n")
        
        # Create repetitive content that compresses well
        for chapter in range(50):
            f.write(f"Chapter {chapter + 1}: File Compression Benefits\n")
            f.write("-" * 40 + "\n")
            
            for paragraph in range(20):
                f.write(f"Paragraph {paragraph + 1}: ")
                f.write("File compression is a crucial feature for efficient data transfer. ")
                f.write("It reduces the amount of data that needs to be transmitted over the network, ")
                f.write("which can significantly improve transfer speeds, especially for text-based files. ")
                f.write("The compression algorithm analyzes the file content and removes redundancy, ")
                f.write("creating a smaller file that contains the same information. ")
                f.write("When the file reaches its destination, it is automatically decompressed ")
                f.write("to restore the original content. This process is transparent to the user ")
                f.write("and provides substantial benefits for large file transfers.\n\n")
            
            f.write("\n")
    
    test_files.append(str(large_text))
    
    # 2. JSON configuration file (compresses well)
    json_file = temp_dir / "config.json"
    with open(json_file, 'w', encoding='utf-8') as f:
        f.write('{\n')
        f.write('  "application": {\n')
        f.write('    "name": "File Transfer Pro",\n')
        f.write('    "version": "1.0.0",\n')
        f.write('    "features": [\n')
        
        features = [
            "Modern professional UI", "File compression", "Enhanced error handling",
            "Connection management", "DDNS support", "Progress tracking",
            "Retry logic", "File validation", "Cross-platform compatibility"
        ]
        
        for i, feature in enumerate(features):
            f.write(f'      "{feature}"')
            if i < len(features) - 1:
                f.write(',')
            f.write('\n')
        
        f.write('    ],\n')
        f.write('    "compression": {\n')
        f.write('      "enabled": true,\n')
        f.write('      "methods": ["gzip", "bzip2", "lzma", "zip"],\n')
        f.write('      "auto_detect": true,\n')
        f.write('      "min_size": 1024\n')
        f.write('    },\n')
        f.write('    "network": {\n')
        f.write('      "chunk_size": 8192,\n')
        f.write('      "max_retries": 3,\n')
        f.write('      "timeout": 10\n')
        f.write('    }\n')
        f.write('  }\n')
        f.write('}\n')
    
    test_files.append(str(json_file))
    
    # 3. Log file (compresses moderately well)
    log_file = temp_dir / "application.log"
    with open(log_file, 'w', encoding='utf-8') as f:
        import datetime
        
        for day in range(7):
            date = datetime.datetime.now() - datetime.timedelta(days=day)
            
            for hour in range(24):
                for minute in range(0, 60, 5):
                    timestamp = date.replace(hour=hour, minute=minute, second=0)
                    
                    log_entries = [
                        "INFO - Application started",
                        "INFO - Server listening on port 8888",
                        "INFO - Client connected from 192.168.1.100",
                        "INFO - File transfer started: document.pdf",
                        "INFO - Transfer progress: 25%",
                        "INFO - Transfer progress: 50%",
                        "INFO - Transfer progress: 75%",
                        "INFO - Transfer completed successfully",
                        "INFO - Client disconnected",
                        "DEBUG - Memory usage: 45MB",
                        "DEBUG - Network throughput: 1.2MB/s"
                    ]
                    
                    for entry in log_entries:
                        f.write(f"{timestamp.strftime('%Y-%m-%d %H:%M:%S')} - FileTransfer - {entry}\n")
    
    test_files.append(str(log_file))
    
    # 4. Binary file (doesn't compress well)
    binary_file = temp_dir / "random_data.bin"
    with open(binary_file, 'wb') as f:
        import random
        # Create pseudo-random binary data
        for _ in range(10000):
            data = bytes([random.randint(0, 255) for _ in range(100)])
            f.write(data)
    
    test_files.append(str(binary_file))
    
    return test_files


def demo_compression_algorithms():
    """Demonstrate different compression algorithms."""
    print("🗜️  Compression Algorithms Demo")
    print("-" * 35)
    
    # Create a test file
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
        # Create content that compresses well
        content = "This is a test file for compression demonstration. " * 1000
        f.write(content)
        test_file = f.name
    
    try:
        compressor = FileCompressor()
        original_size = FileUtils.get_file_size(test_file)
        
        print(f"📄 Test file: {FileUtils.format_file_size(original_size)}")
        print()
        
        # Test different compression methods
        methods = [
            CompressionMethod.GZIP,
            CompressionMethod.BZIP2,
            CompressionMethod.LZMA,
            CompressionMethod.ZIP
        ]
        
        results = []
        
        for method in methods:
            print(f"🔄 Testing {method.value.upper()} compression...")
            
            start_time = time.time()
            result = compressor.compress_file(test_file, method)
            end_time = time.time()
            
            if result.success:
                compression_time = end_time - start_time
                results.append((method, result, compression_time))
                
                print(f"   ✅ Compressed: {FileUtils.format_file_size(result.original_size)} → {FileUtils.format_file_size(result.compressed_size)}")
                print(f"   📊 Ratio: {result.compression_ratio:.3f} ({result.size_reduction_percent:.1f}% reduction)")
                print(f"   ⏱️  Time: {compression_time:.3f}s")
                
                # Clean up compressed file
                import os
                os.unlink(result.compressed_file)
            else:
                print(f"   ❌ Compression failed: {result.error_message}")
            
            print()
        
        # Show best method
        if results:
            best_ratio = min(results, key=lambda x: x[1].compression_ratio)
            fastest = min(results, key=lambda x: x[2])
            
            print("🏆 Results Summary:")
            print(f"   🎯 Best compression: {best_ratio[0].value.upper()} ({best_ratio[1].size_reduction_percent:.1f}% reduction)")
            print(f"   ⚡ Fastest: {fastest[0].value.upper()} ({fastest[2]:.3f}s)")
    
    finally:
        # Clean up test file
        import os
        os.unlink(test_file)


def demo_file_transfer_with_compression():
    """Demonstrate file transfer with compression."""
    print("\n🚀 File Transfer with Compression Demo")
    print("-" * 42)
    
    # Create temporary directories
    with tempfile.TemporaryDirectory() as send_dir, tempfile.TemporaryDirectory() as receive_dir:
        send_path = Path(send_dir)
        receive_path = Path(receive_dir)
        
        print(f"📁 Send directory: {send_path}")
        print(f"📁 Receive directory: {receive_path}")
        print()
        
        # Create test files
        print("📝 Creating test files...")
        test_files = create_test_files_for_compression(send_path)
        
        total_original_size = 0
        for file_path in test_files:
            file_info = FileUtils.get_file_info(file_path)
            total_original_size += file_info['size']
            print(f"   ✓ {file_info['name']} ({file_info['size_human']})")
        
        print(f"\n📊 Total original size: {FileUtils.format_file_size(total_original_size)}")
        print()
        
        # Find available port
        port = NetworkUtils.find_available_port(start_port=9000)
        print(f"🌐 Using port: {port}")
        print()
        
        # Setup server with compression
        print("🖥️  Setting up server with compression...")
        server = FileTransferServer(
            host="localhost",
            port=port,
            download_dir=str(receive_path),
            chunk_size=4096,
            enable_compression=True
        )
        
        # Track server events
        received_files = []
        compression_stats = []
        
        def on_file_received(filename, client_id):
            received_files.append(filename)
            print(f"   ✅ File received: {filename}")
        
        def on_transfer_progress(filename, progress, bytes_received, total_bytes, client_id):
            if int(progress) % 25 == 0:  # Show progress every 25%
                print(f"   📊 {filename}: {progress:.1f}% ({FileUtils.format_file_size(bytes_received)}/{FileUtils.format_file_size(total_bytes)})")
        
        server.on_file_received = on_file_received
        server.on_transfer_progress = on_transfer_progress
        
        # Start server in background thread
        server_thread = threading.Thread(target=server.start, daemon=True)
        server_thread.start()
        
        # Wait for server to start
        time.sleep(0.5)
        print("   ✓ Server started with compression enabled")
        print()
        
        try:
            # Setup client with compression
            print("📱 Setting up client with compression...")
            client = EnhancedFileTransferClient(
                chunk_size=4096,
                enable_compression=True,
                compression_method=CompressionMethod.AUTO
            )
            
            # Track compression statistics
            def on_transfer_complete(filename, success):
                if success:
                    print(f"   ✅ Transfer completed: {filename}")
                else:
                    print(f"   ❌ Transfer failed: {filename}")
            
            client.on_transfer_complete = on_transfer_complete
            
            # Connect to server
            print(f"   🔗 Connecting to localhost:{port}...")
            if not client.connect("localhost", port):
                print("   ❌ Failed to connect to server")
                return
            
            print("   ✓ Connected to server")
            print()
            
            # Transfer files with compression
            print("📤 Starting file transfers with compression...")
            total_files = len(test_files)
            total_compressed_size = 0
            
            for i, file_path in enumerate(test_files, 1):
                filename = Path(file_path).name
                original_size = FileUtils.get_file_size(file_path)
                
                print(f"   📄 Transferring {filename} ({FileUtils.format_file_size(original_size)}) [{i}/{total_files}]")
                
                start_time = time.time()
                success = client.send_file(file_path)
                end_time = time.time()
                
                if success:
                    transfer_time = end_time - start_time
                    
                    # Get compression stats from client
                    if hasattr(client, 'compressor') and client.compressor:
                        stats = client.compressor.get_compression_stats()
                        if stats['files_compressed'] > 0:
                            compressed_size = stats['total_compressed_size']
                            total_compressed_size += compressed_size
                            compression_ratio = compressed_size / original_size
                            
                            print(f"   🗜️  Compression: {compression_ratio:.3f} ratio ({((original_size - compressed_size) / original_size * 100):.1f}% reduction)")
                        else:
                            total_compressed_size += original_size
                            print(f"   ℹ️  File not compressed (no benefit)")
                    
                    speed = original_size / transfer_time if transfer_time > 0 else 0
                    print(f"   ✅ Transfer completed in {transfer_time:.2f}s ({FileUtils.format_file_size(int(speed))}/s)")
                else:
                    print(f"   ❌ Transfer failed")
                    total_compressed_size += original_size
                
                print()
            
            # Disconnect client
            client.disconnect()
            print("   📱 Client disconnected")
            print()
            
            # Show compression summary
            print("📊 Compression Summary:")
            print(f"   📄 Files transferred: {len(test_files)}")
            print(f"   📦 Original total size: {FileUtils.format_file_size(total_original_size)}")
            print(f"   🗜️  Compressed total size: {FileUtils.format_file_size(total_compressed_size)}")
            
            if total_original_size > 0:
                overall_ratio = total_compressed_size / total_original_size
                size_saved = total_original_size - total_compressed_size
                percent_saved = (size_saved / total_original_size) * 100
                
                print(f"   💾 Space saved: {FileUtils.format_file_size(size_saved)} ({percent_saved:.1f}%)")
                print(f"   📈 Overall compression ratio: {overall_ratio:.3f}")
            
            print()
            
            # Verify received files
            print("🔍 Verifying received files...")
            
            for original_file in test_files:
                original_path = Path(original_file)
                received_path = receive_path / original_path.name
                
                if received_path.exists():
                    original_size = FileUtils.get_file_size(str(original_path))
                    received_size = FileUtils.get_file_size(str(received_path))
                    
                    if original_size == received_size:
                        # Verify content
                        original_checksum = FileUtils.calculate_md5(str(original_path))
                        received_checksum = FileUtils.calculate_md5(str(received_path))
                        
                        if original_checksum == received_checksum:
                            print(f"   ✅ {original_path.name}: Perfect match (size and content)")
                        else:
                            print(f"   ❌ {original_path.name}: Content mismatch")
                    else:
                        print(f"   ❌ {original_path.name}: Size mismatch ({original_size} vs {received_size})")
                else:
                    print(f"   ❌ {original_path.name}: File not received")
            
        finally:
            # Stop server
            server.stop()
            print("\n🖥️  Server stopped")


def main():
    """Main demo function."""
    print("🎉 File Transfer Pro - Compression Demo")
    print("=" * 45)
    print()
    
    # Setup logging
    logger = setup_logger(level=20)  # INFO level
    
    try:
        # Demo compression algorithms
        demo_compression_algorithms()
        
        # Demo file transfer with compression
        demo_file_transfer_with_compression()
        
        print("\n🎯 Compression Benefits Summary:")
        print("=" * 40)
        print("✅ Automatic file compression before transfer")
        print("✅ Multiple compression algorithms (GZIP, BZIP2, LZMA, ZIP)")
        print("✅ Intelligent algorithm selection based on file type")
        print("✅ Automatic decompression on receiving side")
        print("✅ Transparent to the user - no manual steps needed")
        print("✅ Significant size reduction for text-based files")
        print("✅ Faster transfers for large documents")
        print("✅ Perfect file integrity preservation")
        print("✅ Fallback to uncompressed for files that don't benefit")
        
        print("\n🚀 UI Improvements:")
        print("✅ Fixed text color contrast issues")
        print("✅ Better visibility across all UI components")
        print("✅ Professional color scheme")
        print("✅ Compression settings in preferences")
        
        print("\n✨ Demo completed successfully!")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

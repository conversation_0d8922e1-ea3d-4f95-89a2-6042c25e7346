"""
Performance and stress tests for the compression system.
"""

import os
import time
import tempfile
import unittest
import threading
import sys
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.utils.compression import FileCompressor, CompressionMethod
from src.utils.file_utils import FileUtils


class TestCompressionPerformance(unittest.TestCase):
    """Performance tests for compression system."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.compressor = FileCompressor()
        self.temp_dir = Path(tempfile.mkdtemp())
        
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        self.compressor.cleanup()
    
    def _create_large_text_file(self, size_mb: int) -> Path:
        """Create a large text file for testing."""
        test_file = self.temp_dir / f"large_{size_mb}mb.txt"
        
        # Create content that compresses well
        base_content = "This is a test line that will be repeated many times to create a large file.\n"
        lines_needed = (size_mb * 1024 * 1024) // len(base_content)
        
        with open(test_file, 'w') as f:
            for _ in range(lines_needed):
                f.write(base_content)
        
        return test_file
    
    def _create_random_file(self, size_mb: int) -> Path:
        """Create a file with random content (doesn't compress well)."""
        test_file = self.temp_dir / f"random_{size_mb}mb.dat"
        
        import random
        with open(test_file, 'wb') as f:
            for _ in range(size_mb * 1024):
                chunk = bytes(random.randint(0, 255) for _ in range(1024))
                f.write(chunk)
        
        return test_file

    def test_compression_speed_comparison(self):
        """Compare compression speeds of different methods."""
        # Create a 5MB test file
        test_file = self._create_large_text_file(5)
        
        methods = [
            CompressionMethod.GZIP,
            CompressionMethod.BZIP2,
            CompressionMethod.LZMA,
            CompressionMethod.ZIP
        ]
        
        results = {}
        
        for method in methods:
            start_time = time.time()
            result = self.compressor.compress_file(str(test_file), method)
            end_time = time.time()
            
            self.assertTrue(result.success, f"Compression failed for {method}")
            
            results[method] = {
                'time': end_time - start_time,
                'ratio': result.compression_ratio,
                'size_reduction': result.size_reduction_percent
            }
            
            print(f"{method.value}: {results[method]['time']:.2f}s, "
                  f"ratio: {results[method]['ratio']:.3f}, "
                  f"reduction: {results[method]['size_reduction']:.1f}%")
        
        # GZIP should generally be fastest
        self.assertLess(results[CompressionMethod.GZIP]['time'], 
                       results[CompressionMethod.LZMA]['time'])

    def test_large_file_compression(self):
        """Test compression of large files."""
        # Create a 50MB file
        test_file = self._create_large_text_file(50)
        
        start_time = time.time()
        result = self.compressor.compress_file(str(test_file), CompressionMethod.GZIP)
        end_time = time.time()
        
        self.assertTrue(result.success)
        self.assertGreater(result.original_size, 50 * 1024 * 1024)  # > 50MB
        self.assertLess(result.compression_ratio, 0.1)  # Should compress well
        
        compression_time = end_time - start_time
        print(f"50MB file compressed in {compression_time:.2f}s")
        
        # Should complete in reasonable time (< 30 seconds)
        self.assertLess(compression_time, 30.0)

    def test_decompression_speed(self):
        """Test decompression speed."""
        # Create and compress a file
        test_file = self._create_large_text_file(10)
        compress_result = self.compressor.compress_file(str(test_file), CompressionMethod.GZIP)
        self.assertTrue(compress_result.success)
        
        # Test decompression speed
        output_file = self.temp_dir / "decompressed.txt"
        
        start_time = time.time()
        success = self.compressor.decompress_file(
            compress_result.compressed_file,
            str(output_file),
            CompressionMethod.GZIP
        )
        end_time = time.time()
        
        self.assertTrue(success)
        decompression_time = end_time - start_time
        print(f"10MB file decompressed in {decompression_time:.2f}s")
        
        # Decompression should be faster than compression
        # and complete in reasonable time
        self.assertLess(decompression_time, 10.0)

    def test_concurrent_compression_performance(self):
        """Test performance under concurrent compression load."""
        # Create multiple test files
        test_files = []
        for i in range(5):
            test_file = self._create_large_text_file(2)  # 2MB each
            test_files.append(test_file)
        
        def compress_file(file_path):
            compressor = FileCompressor()  # Each thread gets its own compressor
            try:
                start_time = time.time()
                result = compressor.compress_file(str(file_path), CompressionMethod.GZIP)
                end_time = time.time()
                return result.success, end_time - start_time
            finally:
                compressor.cleanup()
        
        # Test concurrent compression
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(compress_file, f) for f in test_files]
            results = [future.result() for future in as_completed(futures)]
        end_time = time.time()
        
        # All compressions should succeed
        successes = [r[0] for r in results]
        self.assertTrue(all(successes))
        
        total_time = end_time - start_time
        avg_time = sum(r[1] for r in results) / len(results)
        
        print(f"Concurrent compression: {total_time:.2f}s total, {avg_time:.2f}s average")
        
        # Concurrent should be faster than sequential
        self.assertLess(total_time, avg_time * len(test_files))

    def test_memory_usage_large_files(self):
        """Test memory usage with large files."""
        import psutil
        import gc
        
        # Get initial memory usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        # Create and compress a large file
        test_file = self._create_large_text_file(20)  # 20MB
        
        # Force garbage collection
        gc.collect()
        before_compression = process.memory_info().rss
        
        result = self.compressor.compress_file(str(test_file), CompressionMethod.GZIP)
        self.assertTrue(result.success)
        
        # Check memory after compression
        after_compression = process.memory_info().rss
        
        # Clean up
        self.compressor.cleanup()
        gc.collect()
        final_memory = process.memory_info().rss
        
        memory_increase = after_compression - before_compression
        memory_mb = memory_increase / (1024 * 1024)
        
        print(f"Memory increase during compression: {memory_mb:.1f}MB")
        
        # Memory increase should be reasonable (< 100MB for 20MB file)
        self.assertLess(memory_mb, 100)

    def test_compression_ratio_by_file_type(self):
        """Test compression ratios for different file types."""
        test_cases = [
            ("text.txt", "This is repeated text content. " * 1000, "text"),
            ("code.py", "def function():\n    return 'value'\n" * 500, "code"),
            ("json.json", '{"key": "value", "number": 42}' * 1000, "json"),
            ("random.dat", None, "random")  # Will be filled with random data
        ]
        
        results = {}
        
        for filename, content, file_type in test_cases:
            if content:
                test_file = self.temp_dir / filename
                test_file.write_text(content)
            else:
                test_file = self._create_random_file(1)  # 1MB random file
            
            result = self.compressor.compress_file(str(test_file), CompressionMethod.GZIP)
            self.assertTrue(result.success)
            
            results[file_type] = result.compression_ratio
            print(f"{file_type}: {result.compression_ratio:.3f} ratio, "
                  f"{result.size_reduction_percent:.1f}% reduction")
        
        # Text files should compress better than random data
        self.assertLess(results["text"], results["random"])
        self.assertLess(results["code"], results["random"])
        self.assertLess(results["json"], results["random"])

    def test_compression_threshold_performance(self):
        """Test performance impact of compression threshold decisions."""
        # Test files of various sizes around the threshold
        sizes_kb = [0.5, 1, 2, 5, 10, 50, 100]
        
        for size_kb in sizes_kb:
            # Create file with specified size
            test_file = self.temp_dir / f"test_{size_kb}kb.txt"
            content = "Test content " * int(size_kb * 1024 / 13)  # Approximate size
            test_file.write_text(content)
            
            start_time = time.time()
            result = self.compressor.compress_file(str(test_file))
            end_time = time.time()
            
            compression_time = end_time - start_time
            
            print(f"{size_kb}KB file: {compression_time:.4f}s, "
                  f"method: {result.method_used.value}")
            
            # Very small files should be processed quickly
            if size_kb < 2:
                self.assertLess(compression_time, 0.1)


class TestCompressionStress(unittest.TestCase):
    """Stress tests for compression system."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())
        
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_many_small_files(self):
        """Test compressing many small files."""
        num_files = 100
        compressors = []
        
        try:
            # Create many small files and compress them
            for i in range(num_files):
                test_file = self.temp_dir / f"small_{i}.txt"
                test_file.write_text(f"Small file {i} content " * 10)
                
                compressor = FileCompressor()
                compressors.append(compressor)
                
                result = compressor.compress_file(str(test_file))
                self.assertTrue(result.success)
            
            print(f"Successfully compressed {num_files} small files")
            
        finally:
            # Clean up all compressors
            for compressor in compressors:
                compressor.cleanup()

    def test_compression_under_load(self):
        """Test compression system under heavy load."""
        def compression_worker(worker_id, num_operations):
            compressor = FileCompressor()
            successes = 0
            
            try:
                for i in range(num_operations):
                    # Create a test file
                    test_file = self.temp_dir / f"worker_{worker_id}_file_{i}.txt"
                    content = f"Worker {worker_id} file {i} content " * 100
                    test_file.write_text(content)
                    
                    # Compress it
                    result = compressor.compress_file(str(test_file))
                    if result.success:
                        successes += 1
                    
                    # Occasionally clean up to test resource management
                    if i % 10 == 0:
                        compressor.cleanup()
                        compressor = FileCompressor()
                
                return successes
            finally:
                compressor.cleanup()
        
        # Run multiple workers concurrently
        num_workers = 5
        operations_per_worker = 20
        
        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            futures = [
                executor.submit(compression_worker, i, operations_per_worker)
                for i in range(num_workers)
            ]
            
            results = [future.result() for future in as_completed(futures)]
        
        total_successes = sum(results)
        expected_total = num_workers * operations_per_worker
        
        print(f"Stress test: {total_successes}/{expected_total} operations succeeded")
        
        # Should have high success rate
        success_rate = total_successes / expected_total
        self.assertGreater(success_rate, 0.95)  # > 95% success rate


if __name__ == '__main__':
    unittest.main(verbosity=2)

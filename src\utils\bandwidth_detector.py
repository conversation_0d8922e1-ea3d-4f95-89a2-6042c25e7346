"""
Dynamic bandwidth detection and network performance monitoring.
"""

import time
import socket
import threading
import statistics
from typing import Dict, List, Optional, Tuple, Callable
from dataclasses import dataclass
from enum import Enum
from src.utils.logger import get_logger


class NetworkQuality(Enum):
    """Network quality levels."""
    EXCELLENT = "excellent"
    GOOD = "good"
    FAIR = "fair"
    POOR = "poor"
    VERY_POOR = "very_poor"


@dataclass
class BandwidthMeasurement:
    """Single bandwidth measurement result."""
    timestamp: float
    bytes_transferred: int
    duration: float
    speed_bps: float
    latency_ms: float
    packet_loss: float
    quality: NetworkQuality


@dataclass
class NetworkMetrics:
    """Comprehensive network performance metrics."""
    current_speed_bps: float
    average_speed_bps: float
    peak_speed_bps: float
    available_bandwidth_bps: float
    utilization_percent: float
    latency_ms: float
    jitter_ms: float
    packet_loss_percent: float
    quality: NetworkQuality
    congestion_detected: bool


class BandwidthDetector:
    """
    Dynamic bandwidth detection and network performance monitoring system.
    """
    
    def __init__(self, test_servers: Optional[List[Tuple[str, int]]] = None):
        """
        Initialize the bandwidth detector.
        
        Args:
            test_servers: List of (host, port) tuples for bandwidth testing
        """
        self.logger = get_logger()
        
        # Default test servers for bandwidth detection
        self.test_servers = test_servers or [
            ("httpbin.org", 80),
            ("google.com", 80),
            ("cloudflare.com", 80),
            ("github.com", 80)
        ]
        
        # Measurement history
        self.measurements: List[BandwidthMeasurement] = []
        self.max_history = 50
        
        # Current metrics
        self.current_metrics = NetworkMetrics(
            current_speed_bps=0.0,
            average_speed_bps=0.0,
            peak_speed_bps=0.0,
            available_bandwidth_bps=0.0,
            utilization_percent=0.0,
            latency_ms=0.0,
            jitter_ms=0.0,
            packet_loss_percent=0.0,
            quality=NetworkQuality.FAIR,
            congestion_detected=False
        )
        
        # Monitoring state
        self.monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.monitor_interval = 5.0  # seconds
        
        # Callbacks
        self.on_metrics_updated: Optional[Callable[[NetworkMetrics], None]] = None
        self.on_bandwidth_detected: Optional[Callable[[float], None]] = None
        self.on_quality_changed: Optional[Callable[[NetworkQuality], None]] = None
        
        # Configuration
        self.test_duration = 3.0  # seconds per test
        self.test_chunk_size = 64 * 1024  # 64KB chunks
        self.min_test_bytes = 1024 * 1024  # 1MB minimum for reliable measurement
        
    def start_monitoring(self):
        """Start continuous network monitoring."""
        if self.monitoring:
            return
            
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self.monitor_thread.start()
        self.logger.info("Started bandwidth monitoring")
        
    def stop_monitoring(self):
        """Stop network monitoring."""
        self.monitoring = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=2.0)
        self.logger.info("Stopped bandwidth monitoring")
        
    def detect_available_bandwidth(self) -> float:
        """
        Detect available network bandwidth.
        
        Returns:
            Available bandwidth in bytes per second
        """
        self.logger.info("Starting bandwidth detection...")
        
        best_speed = 0.0
        successful_tests = 0
        
        for host, port in self.test_servers:
            try:
                speed = self._test_bandwidth_to_server(host, port)
                if speed > 0:
                    best_speed = max(best_speed, speed)
                    successful_tests += 1
                    self.logger.debug(f"Bandwidth test to {host}:{port}: {speed:.2f} B/s")
                    
                    # Don't test all servers if we get a good result
                    if successful_tests >= 2 and best_speed > 1024 * 1024:  # 1 MB/s
                        break
                        
            except Exception as e:
                self.logger.warning(f"Bandwidth test failed for {host}:{port}: {e}")
                continue
        
        if best_speed > 0:
            self.current_metrics.available_bandwidth_bps = best_speed
            if self.on_bandwidth_detected:
                self.on_bandwidth_detected(best_speed)
            
            self.logger.info(f"Detected available bandwidth: {best_speed:.2f} B/s "
                           f"({self._format_speed_mbps(best_speed):.1f} Mbps)")
        else:
            self.logger.warning("Failed to detect bandwidth - using default")
            best_speed = 10 * 1024 * 1024  # Default to 10 MB/s
            
        return best_speed
        
    def measure_latency(self, host: str, port: int, samples: int = 5) -> Tuple[float, float]:
        """
        Measure network latency and jitter.
        
        Args:
            host: Target host
            port: Target port
            samples: Number of samples to take
            
        Returns:
            Tuple of (average_latency_ms, jitter_ms)
        """
        latencies = []
        
        for _ in range(samples):
            try:
                start_time = time.time()
                
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                    sock.settimeout(2.0)
                    sock.connect((host, port))
                    
                end_time = time.time()
                latency_ms = (end_time - start_time) * 1000
                latencies.append(latency_ms)
                
                time.sleep(0.1)  # Small delay between samples
                
            except Exception:
                continue
        
        if not latencies:
            return 0.0, 0.0
            
        avg_latency = statistics.mean(latencies)
        jitter = statistics.stdev(latencies) if len(latencies) > 1 else 0.0
        
        return avg_latency, jitter
        
    def get_current_metrics(self) -> NetworkMetrics:
        """Get current network performance metrics."""
        return self.current_metrics
        
    def _test_bandwidth_to_server(self, host: str, port: int) -> float:
        """Test bandwidth to a specific server."""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(5.0)
                
                # Optimize socket for testing
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
                
                # Connect and send HTTP request
                sock.connect((host, port))
                
                # Send a simple HTTP request to download data
                request = f"GET / HTTP/1.1\r\nHost: {host}\r\nConnection: close\r\n\r\n"
                sock.send(request.encode())
                
                # Measure download speed
                start_time = time.time()
                total_bytes = 0
                
                while time.time() - start_time < self.test_duration:
                    try:
                        data = sock.recv(self.test_chunk_size)
                        if not data:
                            break
                        total_bytes += len(data)
                    except socket.timeout:
                        break
                
                duration = time.time() - start_time
                
                if duration > 0 and total_bytes >= self.min_test_bytes:
                    return total_bytes / duration
                    
        except Exception as e:
            self.logger.debug(f"Bandwidth test error for {host}:{port}: {e}")
            
        return 0.0
        
    def _monitoring_loop(self):
        """Main monitoring loop."""
        while self.monitoring:
            try:
                # Update metrics
                self._update_metrics()
                
                # Sleep until next update
                time.sleep(self.monitor_interval)
                
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                time.sleep(1.0)
                
    def _update_metrics(self):
        """Update current network metrics."""
        # Measure latency to first available server
        if self.test_servers:
            host, port = self.test_servers[0]
            latency, jitter = self.measure_latency(host, port, samples=3)
            self.current_metrics.latency_ms = latency
            self.current_metrics.jitter_ms = jitter
        
        # Update quality assessment
        self._assess_network_quality()
        
        # Notify listeners
        if self.on_metrics_updated:
            self.on_metrics_updated(self.current_metrics)
            
    def _assess_network_quality(self):
        """Assess overall network quality."""
        latency = self.current_metrics.latency_ms
        jitter = self.current_metrics.jitter_ms
        packet_loss = self.current_metrics.packet_loss_percent
        
        # Determine quality based on metrics
        if latency < 50 and jitter < 10 and packet_loss < 1:
            quality = NetworkQuality.EXCELLENT
        elif latency < 100 and jitter < 20 and packet_loss < 3:
            quality = NetworkQuality.GOOD
        elif latency < 200 and jitter < 50 and packet_loss < 5:
            quality = NetworkQuality.FAIR
        elif latency < 500 and jitter < 100 and packet_loss < 10:
            quality = NetworkQuality.POOR
        else:
            quality = NetworkQuality.VERY_POOR
            
        # Detect congestion
        congestion = (latency > 200 or jitter > 50 or packet_loss > 5)
        
        old_quality = self.current_metrics.quality
        self.current_metrics.quality = quality
        self.current_metrics.congestion_detected = congestion
        
        # Notify if quality changed
        if quality != old_quality and self.on_quality_changed:
            self.on_quality_changed(quality)
            
    def _format_speed_mbps(self, bytes_per_second: float) -> float:
        """Convert bytes per second to Mbps."""
        return (bytes_per_second * 8) / (1024 * 1024)

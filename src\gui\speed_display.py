"""
Enhanced speed display widget with real-time monitoring and graphs.
"""

import tkinter as tk
from tkinter import ttk
import time
from typing import List, Optional, Tuple
from collections import deque
from src.utils.file_utils import FileUtils


class SpeedDisplayWidget(ttk.Frame):
    """
    Enhanced speed display widget showing real-time transfer speeds,
    network utilization, and speed graphs.
    """
    
    def __init__(self, parent, **kwargs):
        """
        Initialize the speed display widget.
        
        Args:
            parent: Parent widget
            **kwargs: Additional arguments for ttk.Frame
        """
        super().__init__(parent, **kwargs)
        
        # Speed tracking
        self.current_speed = 0.0  # bytes per second
        self.average_speed = 0.0
        self.peak_speed = 0.0
        self.available_bandwidth = 0.0
        self.utilization_percent = 0.0
        
        # Speed history for graph
        self.speed_history: deque = deque(maxlen=60)  # Last 60 seconds
        self.max_history_speed = 1.0
        
        # Display settings
        self.show_graph = True
        self.show_utilization = True
        self.compact_mode = False
        
        self._create_widgets()
        
    def _create_widgets(self):
        """Create the display widgets."""
        # Main container
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Speed info frame
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill=tk.X, pady=(0, 5))
        
        # Current speed (large display)
        self.current_speed_label = ttk.Label(
            info_frame,
            text="0 B/s",
            font=('Segoe UI', 12, 'bold'),
            foreground='#2E7D32'
        )
        self.current_speed_label.pack(side=tk.LEFT)
        
        # Speed in Mbps
        self.mbps_label = ttk.Label(
            info_frame,
            text="(0 Mbps)",
            font=('Segoe UI', 9),
            foreground='#666666'
        )
        self.mbps_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # ETA display
        self.eta_label = ttk.Label(
            info_frame,
            text="",
            font=('Segoe UI', 9),
            foreground='#1976D2'
        )
        self.eta_label.pack(side=tk.RIGHT)
        
        # Statistics frame
        stats_frame = ttk.Frame(main_frame)
        stats_frame.pack(fill=tk.X, pady=(0, 5))
        
        # Create statistics grid
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill=tk.X)
        
        # Configure grid columns
        stats_grid.columnconfigure(1, weight=1)
        stats_grid.columnconfigure(3, weight=1)
        
        # Average speed
        ttk.Label(stats_grid, text="Avg:", font=('Segoe UI', 8)).grid(
            row=0, column=0, sticky=tk.W, padx=(0, 5)
        )
        self.avg_speed_label = ttk.Label(stats_grid, text="0 B/s", font=('Segoe UI', 8))
        self.avg_speed_label.grid(row=0, column=1, sticky=tk.W)
        
        # Peak speed
        ttk.Label(stats_grid, text="Peak:", font=('Segoe UI', 8)).grid(
            row=0, column=2, sticky=tk.W, padx=(20, 5)
        )
        self.peak_speed_label = ttk.Label(stats_grid, text="0 B/s", font=('Segoe UI', 8))
        self.peak_speed_label.grid(row=0, column=3, sticky=tk.W)
        
        # Utilization frame
        self.utilization_frame = ttk.Frame(main_frame)
        self.utilization_frame.pack(fill=tk.X, pady=(0, 5))
        
        # Utilization label
        ttk.Label(self.utilization_frame, text="Network Utilization:", font=('Segoe UI', 8)).pack(side=tk.LEFT)
        
        # Utilization progress bar
        self.utilization_progress = ttk.Progressbar(
            self.utilization_frame,
            mode='determinate',
            length=100
        )
        self.utilization_progress.pack(side=tk.LEFT, padx=(5, 5))
        
        # Utilization percentage
        self.utilization_label = ttk.Label(
            self.utilization_frame,
            text="0%",
            font=('Segoe UI', 8)
        )
        self.utilization_label.pack(side=tk.LEFT)
        
        # Speed graph frame
        self.graph_frame = ttk.LabelFrame(main_frame, text="Speed Graph", padding="5")
        self.graph_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create canvas for speed graph
        self.graph_canvas = tk.Canvas(
            self.graph_frame,
            height=80,
            bg='white',
            highlightthickness=1,
            highlightbackground='#CCCCCC'
        )
        self.graph_canvas.pack(fill=tk.BOTH, expand=True)
        
        # Bind canvas resize
        self.graph_canvas.bind('<Configure>', self._on_canvas_resize)
        
    def update_speed(self, current_speed: float, average_speed: float = None, 
                    peak_speed: float = None, eta: str = None):
        """
        Update speed display.
        
        Args:
            current_speed: Current transfer speed in bytes per second
            average_speed: Average speed in bytes per second
            peak_speed: Peak speed in bytes per second
            eta: Estimated time remaining
        """
        self.current_speed = current_speed
        
        if average_speed is not None:
            self.average_speed = average_speed
            
        if peak_speed is not None:
            self.peak_speed = max(self.peak_speed, peak_speed)
        
        # Update labels
        self.current_speed_label.config(text=FileUtils.format_transfer_speed(current_speed))
        self.mbps_label.config(text=f"({FileUtils.format_speed_mbps(current_speed)})")
        
        if average_speed is not None:
            self.avg_speed_label.config(text=FileUtils.format_transfer_speed(average_speed))
            
        self.peak_speed_label.config(text=FileUtils.format_transfer_speed(self.peak_speed))
        
        if eta:
            self.eta_label.config(text=f"ETA: {eta}")
        else:
            self.eta_label.config(text="")
            
        # Add to speed history
        self.speed_history.append(current_speed)
        self.max_history_speed = max(self.max_history_speed, current_speed)
        
        # Update graph
        self._update_graph()
        
    def update_utilization(self, utilization_percent: float, available_bandwidth: float = None):
        """
        Update network utilization display.
        
        Args:
            utilization_percent: Network utilization percentage (0-100)
            available_bandwidth: Available bandwidth in bytes per second
        """
        self.utilization_percent = max(0, min(100, utilization_percent))
        
        if available_bandwidth is not None:
            self.available_bandwidth = available_bandwidth
            
        # Update utilization display
        self.utilization_progress['value'] = self.utilization_percent
        self.utilization_label.config(text=f"{self.utilization_percent:.1f}%")
        
        # Color code utilization
        if self.utilization_percent > 90:
            color = '#D32F2F'  # Red
        elif self.utilization_percent > 70:
            color = '#F57C00'  # Orange
        else:
            color = '#388E3C'  # Green
            
        self.utilization_label.config(foreground=color)
        
    def set_compact_mode(self, compact: bool):
        """
        Set compact display mode.
        
        Args:
            compact: True for compact mode
        """
        self.compact_mode = compact
        
        if compact:
            self.graph_frame.pack_forget()
            self.utilization_frame.pack_forget()
        else:
            self.utilization_frame.pack(fill=tk.X, pady=(0, 5))
            self.graph_frame.pack(fill=tk.BOTH, expand=True)
            
    def set_show_graph(self, show: bool):
        """
        Show or hide the speed graph.
        
        Args:
            show: True to show graph
        """
        self.show_graph = show
        
        if show and not self.compact_mode:
            self.graph_frame.pack(fill=tk.BOTH, expand=True)
        else:
            self.graph_frame.pack_forget()
            
    def set_show_utilization(self, show: bool):
        """
        Show or hide network utilization.
        
        Args:
            show: True to show utilization
        """
        self.show_utilization = show
        
        if show and not self.compact_mode:
            self.utilization_frame.pack(fill=tk.X, pady=(0, 5))
        else:
            self.utilization_frame.pack_forget()
            
    def reset(self):
        """Reset all speed displays."""
        self.current_speed = 0.0
        self.average_speed = 0.0
        self.peak_speed = 0.0
        self.utilization_percent = 0.0
        self.speed_history.clear()
        self.max_history_speed = 1.0
        
        # Update displays
        self.current_speed_label.config(text="0 B/s")
        self.mbps_label.config(text="(0 Mbps)")
        self.avg_speed_label.config(text="0 B/s")
        self.peak_speed_label.config(text="0 B/s")
        self.eta_label.config(text="")
        self.utilization_progress['value'] = 0
        self.utilization_label.config(text="0%")
        
        # Clear graph
        self.graph_canvas.delete("all")
        
    def _update_graph(self):
        """Update the speed graph."""
        if not self.show_graph or not self.speed_history:
            return
            
        # Clear canvas
        self.graph_canvas.delete("all")
        
        # Get canvas dimensions
        width = self.graph_canvas.winfo_width()
        height = self.graph_canvas.winfo_height()
        
        if width <= 1 or height <= 1:
            return
            
        # Calculate scale
        max_speed = max(self.max_history_speed, max(self.speed_history) if self.speed_history else 1.0)
        if max_speed == 0:
            max_speed = 1.0
            
        # Draw grid lines
        self._draw_grid(width, height, max_speed)
        
        # Draw speed line
        if len(self.speed_history) > 1:
            points = []
            for i, speed in enumerate(self.speed_history):
                x = (i / (len(self.speed_history) - 1)) * (width - 20) + 10
                y = height - 10 - ((speed / max_speed) * (height - 20))
                points.extend([x, y])
                
            if len(points) >= 4:
                self.graph_canvas.create_line(
                    points,
                    fill='#1976D2',
                    width=2,
                    smooth=True
                )
                
    def _draw_grid(self, width: int, height: int, max_speed: float):
        """Draw grid lines on the graph."""
        # Horizontal grid lines
        for i in range(5):
            y = 10 + (i * (height - 20) / 4)
            self.graph_canvas.create_line(
                10, y, width - 10, y,
                fill='#E0E0E0',
                width=1
            )
            
            # Speed labels
            speed = max_speed * (1 - i / 4)
            label = FileUtils.format_transfer_speed(speed)
            self.graph_canvas.create_text(
                5, y,
                text=label,
                anchor=tk.E,
                font=('Segoe UI', 7),
                fill='#666666'
            )
            
        # Vertical grid lines
        for i in range(6):
            x = 10 + (i * (width - 20) / 5)
            self.graph_canvas.create_line(
                x, 10, x, height - 10,
                fill='#E0E0E0',
                width=1
            )
            
    def _on_canvas_resize(self, event):
        """Handle canvas resize event."""
        self._update_graph()

"""
Test suite for speed optimization system.
"""

import unittest
import tempfile
import time
import threading
from pathlib import Path
from unittest.mock import Mock, patch

# Add src to path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.utils.bandwidth_detector import BandwidthDetector, NetworkMetrics, NetworkQuality
from src.utils.speed_controller import AdaptiveSpeedController, SpeedSettings, SpeedMode
from src.utils.network_quality_monitor import NetworkQualityMonitor, QualityLevel
from src.core.resilient_client import ResilientFileTransferClient


class TestBandwidthDetector(unittest.TestCase):
    """Test bandwidth detection functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.detector = BandwidthDetector()
        
    def tearDown(self):
        """Clean up after tests."""
        self.detector.stop_monitoring()
        
    def test_initialization(self):
        """Test bandwidth detector initialization."""
        self.assertIsNotNone(self.detector.test_servers)
        self.assertEqual(self.detector.monitoring, False)
        self.assertEqual(self.detector.current_metrics.current_speed_bps, 0.0)
        
    def test_start_stop_monitoring(self):
        """Test starting and stopping monitoring."""
        self.detector.start_monitoring()
        self.assertTrue(self.detector.monitoring)
        
        self.detector.stop_monitoring()
        self.assertFalse(self.detector.monitoring)
        
    def test_bandwidth_detection(self):
        """Test bandwidth detection."""
        # Mock the bandwidth test to return a known value
        with patch.object(self.detector, '_test_bandwidth_to_server', return_value=1024*1024):  # 1 MB/s
            bandwidth = self.detector.detect_available_bandwidth()
            self.assertGreater(bandwidth, 0)
            
    def test_metrics_format(self):
        """Test metrics formatting."""
        metrics = self.detector.get_current_metrics()
        self.assertIsInstance(metrics, NetworkMetrics)
        self.assertIsInstance(metrics.quality, NetworkQuality)


class TestSpeedController(unittest.TestCase):
    """Test adaptive speed controller functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.detector = BandwidthDetector()
        self.controller = AdaptiveSpeedController(self.detector)
        
    def tearDown(self):
        """Clean up after tests."""
        self.controller.stop_optimization()
        
    def test_initialization(self):
        """Test speed controller initialization."""
        self.assertIsNotNone(self.controller.bandwidth_detector)
        self.assertIsNotNone(self.controller.settings)
        self.assertEqual(self.controller.settings.mode, SpeedMode.BALANCED)
        
    def test_configuration(self):
        """Test speed controller configuration."""
        settings = SpeedSettings(
            max_speed_bps=10*1024*1024,  # 10 MB/s
            mode=SpeedMode.AGGRESSIVE,
            enable_congestion_control=True
        )
        
        self.controller.configure(settings)
        self.assertEqual(self.controller.settings.mode, SpeedMode.AGGRESSIVE)
        self.assertEqual(self.controller.settings.max_speed_bps, 10*1024*1024)
        
    def test_parameter_calculation(self):
        """Test parameter calculation."""
        # Mock available bandwidth
        with patch.object(self.detector, 'detect_available_bandwidth', return_value=10*1024*1024):
            self.controller.start_optimization()
            
            params = self.controller.get_current_parameters()
            self.assertGreater(params.chunk_size, 0)
            self.assertGreater(params.buffer_size, 0)
            self.assertGreater(params.target_speed_bps, 0)
            
    def test_throttling(self):
        """Test speed throttling."""
        settings = SpeedSettings(max_speed_bps=1024*1024)  # 1 MB/s limit
        self.controller.configure(settings)
        
        # Test with speed above limit
        self.assertTrue(self.controller.should_throttle(2*1024*1024))  # 2 MB/s
        
        # Test with speed below limit
        self.assertFalse(self.controller.should_throttle(512*1024))  # 512 KB/s
        
    def test_throttle_delay_calculation(self):
        """Test throttle delay calculation."""
        settings = SpeedSettings(max_speed_bps=1024*1024)  # 1 MB/s limit
        self.controller.configure(settings)
        
        # Calculate delay for 2 MB/s with 64KB chunk
        delay = self.controller.calculate_throttle_delay(2*1024*1024, 64*1024)
        self.assertGreaterEqual(delay, 0)
        
    def test_performance_updates(self):
        """Test performance update handling."""
        # Start optimization
        with patch.object(self.detector, 'detect_available_bandwidth', return_value=10*1024*1024):
            self.controller.start_optimization()
            
        # Update with performance data
        self.controller.update_transfer_performance(1024*1024, 1.0)  # 1 MB in 1 second
        
        # Check that recent speeds are tracked
        self.assertGreater(len(self.controller.recent_speeds), 0)


class TestNetworkQualityMonitor(unittest.TestCase):
    """Test network quality monitoring functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.monitor = NetworkQualityMonitor()
        
    def tearDown(self):
        """Clean up after tests."""
        self.monitor.stop_monitoring()
        
    def test_initialization(self):
        """Test quality monitor initialization."""
        self.assertIsNotNone(self.monitor.test_hosts)
        self.assertEqual(self.monitor.monitoring, False)
        
    def test_quality_assessment(self):
        """Test quality level assessment."""
        # Test excellent quality
        quality = self.monitor._assess_quality(30, 5, 0.5)  # Low latency, jitter, loss
        self.assertEqual(quality, QualityLevel.EXCELLENT)
        
        # Test poor quality
        quality = self.monitor._assess_quality(300, 80, 8)  # High latency, jitter, loss
        self.assertEqual(quality, QualityLevel.POOR)
        
    def test_quality_score(self):
        """Test quality score calculation."""
        # Mock current metrics
        self.monitor.current_metrics.quality_level = QualityLevel.EXCELLENT
        self.monitor.current_metrics.latency_ms = 20
        
        score = self.monitor.get_quality_score()
        self.assertGreaterEqual(score, 90)  # Excellent should be 90+
        
    def test_start_stop_monitoring(self):
        """Test starting and stopping monitoring."""
        self.monitor.start_monitoring()
        self.assertTrue(self.monitor.monitoring)
        
        self.monitor.stop_monitoring()
        self.assertFalse(self.monitor.monitoring)


class TestResilientClientSpeedOptimization(unittest.TestCase):
    """Test speed optimization integration with resilient client."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.client = ResilientFileTransferClient(state_dir=self.temp_dir)
        
    def tearDown(self):
        """Clean up after tests."""
        self.client.disconnect()
        # Clean up temp directory
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        
    def test_speed_optimization_configuration(self):
        """Test speed optimization configuration."""
        settings = {
            "enable_speed_optimization": True,
            "max_speed_mbps": 10,
            "speed_mode": "aggressive",
            "enable_congestion_control": True,
            "enable_speed_ramping": True
        }
        
        self.client.configure_speed_optimization(settings)
        self.assertTrue(self.client.speed_optimization_enabled)
        
    def test_socket_optimization(self):
        """Test socket buffer optimization."""
        # Mock socket
        mock_socket = Mock()
        self.client.socket = mock_socket
        
        # Test buffer optimization
        self.client._optimize_socket_buffers(1024*1024)
        
        # Verify socket options were set
        mock_socket.setsockopt.assert_called()
        
    def test_speed_tracking_integration(self):
        """Test speed tracking with optimization."""
        # Configure speed optimization
        settings = {
            "enable_speed_optimization": True,
            "speed_mode": "balanced"
        }
        self.client.configure_speed_optimization(settings)
        
        # Mock speed controller
        self.client.speed_controller = Mock()
        
        # Update speed tracking
        self.client._update_speed_tracking(1024)
        
        # Should update speed controller when optimization is enabled
        # (This would be called after speed calculation)


class TestSpeedOptimizationIntegration(unittest.TestCase):
    """Test integration between all speed optimization components."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.detector = BandwidthDetector()
        self.controller = AdaptiveSpeedController(self.detector)
        self.monitor = NetworkQualityMonitor()
        
    def tearDown(self):
        """Clean up after tests."""
        self.controller.stop_optimization()
        self.monitor.stop_monitoring()
        
    def test_full_optimization_cycle(self):
        """Test complete optimization cycle."""
        # Mock bandwidth detection
        with patch.object(self.detector, 'detect_available_bandwidth', return_value=10*1024*1024):
            # Start optimization
            self.controller.start_optimization()
            
            # Simulate performance updates
            self.controller.update_transfer_performance(1024*1024, 1.0)
            
            # Get optimized parameters
            params = self.controller.get_current_parameters()
            
            # Verify parameters are reasonable
            self.assertGreater(params.chunk_size, 1024)  # At least 1KB
            self.assertLess(params.chunk_size, 10*1024*1024)  # Less than 10MB
            self.assertGreater(params.target_speed_bps, 0)
            
    def test_quality_degradation_response(self):
        """Test response to network quality degradation."""
        # Start optimization
        with patch.object(self.detector, 'detect_available_bandwidth', return_value=10*1024*1024):
            self.controller.start_optimization()
            
        # Get initial parameters
        initial_params = self.controller.get_current_parameters()
        
        # Simulate quality degradation
        from src.utils.network_quality_monitor import QualityMetrics
        poor_metrics = QualityMetrics(
            latency_ms=500,
            jitter_ms=100,
            packet_loss_percent=10,
            quality_level=QualityLevel.POOR,
            timestamp=time.time()
        )
        
        self.controller._on_quality_updated(poor_metrics)
        
        # Parameters should be adjusted for poor quality
        self.assertTrue(self.controller.congestion_detected)


if __name__ == '__main__':
    # Run tests
    unittest.main(verbosity=2)

#!/usr/bin/env python3
"""
Demonstration script for interrupt-resilient file transfer functionality.
"""

import sys
import time
import threading
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.resilient_client import ResilientFileTransferClient
from src.core.resilient_server import ResilientFileTransferServer
from src.utils.logger import get_logger


def create_test_file(file_path: str, size_mb: int = 10):
    """Create a test file of specified size."""
    print(f"Creating test file: {file_path} ({size_mb} MB)")
    
    with open(file_path, 'wb') as f:
        # Write 1MB chunks
        chunk = b'A' * (1024 * 1024)  # 1MB of 'A's
        for i in range(size_mb):
            f.write(chunk)
            print(f"  Written {i+1}/{size_mb} MB")
    
    print(f"Test file created: {file_path}")


def demo_server():
    """Demonstrate resilient server functionality."""
    print("\n=== Starting Resilient Server Demo ===")
    
    # Create download directory
    download_dir = Path("demo_downloads")
    download_dir.mkdir(exist_ok=True)
    
    # Initialize server
    server = ResilientFileTransferServer(
        host="localhost",
        port=8889,
        download_dir=str(download_dir),
        chunk_size=4096  # Smaller chunks for demo
    )
    
    # Setup callbacks
    def on_client_connected(client_id, address):
        print(f"Client connected: {client_id} from {address}")
    
    def on_file_received(filename, client_id):
        print(f"File received: {filename} from {client_id}")
    
    def on_transfer_progress(filename, progress, bytes_received, total_bytes, client_id):
        print(f"Progress: {filename} - {progress:.1f}% ({bytes_received}/{total_bytes} bytes)")
    
    def on_transfer_resumed(transfer_id, client_id):
        print(f"Transfer resumed: {transfer_id} from {client_id}")
    
    server.on_client_connected = on_client_connected
    server.on_file_received = on_file_received
    server.on_transfer_progress = on_transfer_progress
    server.on_transfer_resumed = on_transfer_resumed
    
    # Start server in thread
    server_thread = threading.Thread(target=server.start, daemon=True)
    server_thread.start()
    
    print(f"Server started on localhost:8889")
    print(f"Download directory: {download_dir.absolute()}")
    
    return server


def demo_client():
    """Demonstrate resilient client functionality."""
    print("\n=== Starting Resilient Client Demo ===")
    
    # Create test file
    test_file = "demo_test_file.txt"
    create_test_file(test_file, 5)  # 5MB test file
    
    # Initialize client
    client = ResilientFileTransferClient(
        chunk_size=4096,  # Smaller chunks for demo
        max_retries=3
    )
    
    # Setup callbacks
    def on_connected(host, port):
        print(f"Connected to {host}:{port}")
    
    def on_disconnected():
        print("Disconnected from server")
    
    def on_transfer_progress(filename, progress, bytes_sent, total_bytes):
        print(f"Sending: {filename} - {progress:.1f}% ({bytes_sent}/{total_bytes} bytes)")
    
    def on_transfer_complete(filename, success):
        if success:
            print(f"Transfer completed successfully: {filename}")
        else:
            print(f"Transfer failed: {filename}")
    
    def on_transfer_error(filename, error):
        print(f"Transfer error for {filename}: {error}")
    
    def on_transfer_paused(transfer_id):
        print(f"Transfer paused: {transfer_id}")
    
    def on_transfer_resumed(transfer_id):
        print(f"Transfer resumed: {transfer_id}")
    
    client.on_connected = on_connected
    client.on_disconnected = on_disconnected
    client.on_transfer_progress = on_transfer_progress
    client.on_transfer_complete = on_transfer_complete
    client.on_transfer_error = on_transfer_error
    client.on_transfer_paused = on_transfer_paused
    client.on_transfer_resumed = on_transfer_resumed
    
    return client, test_file


def demo_basic_transfer():
    """Demonstrate basic resilient transfer."""
    print("\n=== Demo: Basic Resilient Transfer ===")
    
    # Start server
    server = demo_server()
    time.sleep(1)  # Let server start
    
    # Start client
    client, test_file = demo_client()
    
    try:
        # Connect to server
        if client.connect("localhost", 8889):
            print("Client connected successfully")
            
            # Send file
            print(f"Starting transfer of {test_file}")
            success = client.send_file(test_file, resume=True)
            
            if success:
                print("Transfer completed successfully!")
            else:
                print("Transfer failed!")
        else:
            print("Failed to connect to server")
    
    finally:
        client.disconnect()
        server.stop()
        
        # Cleanup
        if Path(test_file).exists():
            Path(test_file).unlink()


def demo_resume_transfer():
    """Demonstrate transfer resume functionality."""
    print("\n=== Demo: Transfer Resume Functionality ===")
    
    # Start server
    server = demo_server()
    time.sleep(1)
    
    # Start client
    client, test_file = demo_client()
    
    try:
        # Connect and start transfer
        if client.connect("localhost", 8889):
            print("Starting initial transfer...")
            
            # Start transfer in thread so we can interrupt it
            transfer_thread = threading.Thread(
                target=lambda: client.send_file(test_file, resume=True),
                daemon=True
            )
            transfer_thread.start()
            
            # Let it transfer for a bit
            time.sleep(2)
            
            # Simulate interruption by pausing
            print("Simulating network interruption...")
            active_transfers = client.get_active_transfers()
            if active_transfers:
                transfer_id = active_transfers[0]['transfer_id']
                client.pause_transfer(transfer_id)
                print(f"Transfer paused: {transfer_id}")
                
                # Wait a bit
                time.sleep(1)
                
                # Resume transfer
                print("Resuming transfer...")
                client.resume_transfer(transfer_id)
                
                # Wait for completion
                transfer_thread.join(timeout=10)
                
                print("Transfer resume demo completed!")
            else:
                print("No active transfers found")
        else:
            print("Failed to connect to server")
    
    finally:
        client.disconnect()
        server.stop()
        
        # Cleanup
        if Path(test_file).exists():
            Path(test_file).unlink()


def demo_state_persistence():
    """Demonstrate transfer state persistence."""
    print("\n=== Demo: Transfer State Persistence ===")
    
    # Start server
    server = demo_server()
    time.sleep(1)
    
    # Create client and test file
    client, test_file = demo_client()
    
    try:
        # Connect and start transfer
        if client.connect("localhost", 8889):
            print("Starting transfer with state persistence...")
            
            # Start transfer
            transfer_thread = threading.Thread(
                target=lambda: client.send_file(test_file, resume=True),
                daemon=True
            )
            transfer_thread.start()
            
            # Let it transfer partially
            time.sleep(1)
            
            # Get active transfers to show state
            active_transfers = client.get_active_transfers()
            if active_transfers:
                transfer = active_transfers[0]
                print(f"Transfer state: {transfer['file_name']} - {transfer['progress']:.1f}% complete")
                
                # Show that state is persisted
                print("Transfer state has been saved to disk for resume capability")
                
                # Wait for completion
                transfer_thread.join(timeout=10)
            
            print("State persistence demo completed!")
        else:
            print("Failed to connect to server")
    
    finally:
        client.disconnect()
        server.stop()
        
        # Cleanup
        if Path(test_file).exists():
            Path(test_file).unlink()


def main():
    """Run all demonstrations."""
    print("=== Interrupt-Resilient File Transfer Demo ===")
    print("This demo shows the new resilient transfer capabilities:")
    print("1. Basic resilient transfer")
    print("2. Transfer resume functionality")
    print("3. Transfer state persistence")
    print()
    
    try:
        # Run demos
        demo_basic_transfer()
        time.sleep(2)
        
        demo_resume_transfer()
        time.sleep(2)
        
        demo_state_persistence()
        
        print("\n=== All Demos Completed Successfully! ===")
        print("\nKey Features Demonstrated:")
        print("✓ Interrupt-resilient file transfers")
        print("✓ Automatic state persistence")
        print("✓ Transfer resume capability")
        print("✓ Network disconnection handling")
        print("✓ Segment-based transfer tracking")
        
    except KeyboardInterrupt:
        print("\nDemo interrupted by user")
    except Exception as e:
        print(f"\nDemo error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

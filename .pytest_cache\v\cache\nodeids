["tests/test_compression.py::TestCompressionIntegration::test_compression_result_serialization", "tests/test_compression.py::TestCompressionIntegration::test_compression_with_file_utils", "tests/test_compression.py::TestFileCompressor::test_already_compressed_file", "tests/test_compression.py::TestFileCompressor::test_cleanup_functionality", "tests/test_compression.py::TestFileCompressor::test_compress_auto_method_selection", "tests/test_compression.py::TestFileCompressor::test_compress_binary_file", "tests/test_compression.py::TestFileCompressor::test_compress_empty_file", "tests/test_compression.py::TestFileCompressor::test_compress_large_file", "tests/test_compression.py::TestFileCompressor::test_compress_nonexistent_file", "tests/test_compression.py::TestFileCompressor::test_compress_text_file_bzip2", "tests/test_compression.py::TestFileCompressor::test_compress_text_file_gzip", "tests/test_compression.py::TestFileCompressor::test_compress_text_file_lzma", "tests/test_compression.py::TestFileCompressor::test_compress_text_file_zip", "tests/test_compression.py::TestFileCompressor::test_compression_method_selection_by_size", "tests/test_compression.py::TestFileCompressor::test_compression_ratio_calculation", "tests/test_compression.py::TestFileCompressor::test_compression_statistics", "tests/test_compression.py::TestFileCompressor::test_concurrent_compression", "tests/test_compression.py::TestFileCompressor::test_decompress_bzip2", "tests/test_compression.py::TestFileCompressor::test_decompress_gzip", "tests/test_compression.py::TestFileCompressor::test_decompress_invalid_file", "tests/test_compression.py::TestFileCompressor::test_decompress_lzma", "tests/test_compression.py::TestFileCompressor::test_decompress_none_method", "tests/test_compression.py::TestFileCompressor::test_decompress_to_readonly_directory", "tests/test_compression.py::TestFileCompressor::test_decompress_zip", "tests/test_compression.py::TestFileCompressor::test_should_compress_logic", "tests/test_compression.py::TestFileCompressor::test_small_file_no_compression", "tests/test_compression_end_to_end.py::TestCompressionEndToEnd::test_compression_error_handling", "tests/test_compression_integration.py::TestCompressionIntegration::test_file_transfer_with_compression", "tests/test_compression_performance.py::TestCompressionPerformance::test_compression_ratio_by_file_type", "tests/test_compression_performance.py::TestCompressionPerformance::test_compression_speed_comparison", "tests/test_compression_performance.py::TestCompressionPerformance::test_compression_threshold_performance", "tests/test_compression_performance.py::TestCompressionPerformance::test_concurrent_compression_performance", "tests/test_compression_performance.py::TestCompressionPerformance::test_decompression_speed", "tests/test_compression_performance.py::TestCompressionStress::test_compression_under_load", "tests/test_compression_performance.py::TestCompressionStress::test_many_small_files", "tests/test_core.py::TestEncryption::test_aes_encryption", "tests/test_core.py::TestEncryption::test_rsa_key_exchange", "tests/test_core.py::TestFileTransfer::test_file_transfer", "tests/test_core.py::TestFileTransfer::test_server_client_connection", "tests/test_core.py::TestFileTransferProtocol::test_create_and_parse_message", "tests/test_core.py::TestFileTransferProtocol::test_file_metadata", "tests/test_core.py::TestFileUtils::test_calculate_md5", "tests/test_core.py::TestFileUtils::test_format_file_size", "tests/test_core.py::TestFileUtils::test_is_safe_filename", "tests/test_core.py::TestFileUtils::test_sanitize_filename", "tests/test_core.py::TestNetworkUtils::test_find_available_port", "tests/test_core.py::TestNetworkUtils::test_is_valid_ip", "tests/test_core.py::TestNetworkUtils::test_is_valid_port", "tests/test_speed_optimization.py::TestBandwidthDetector::test_bandwidth_detection", "tests/test_speed_optimization.py::TestBandwidthDetector::test_initialization", "tests/test_speed_optimization.py::TestBandwidthDetector::test_metrics_format", "tests/test_speed_optimization.py::TestBandwidthDetector::test_start_stop_monitoring", "tests/test_speed_optimization.py::TestNetworkQualityMonitor::test_initialization", "tests/test_speed_optimization.py::TestNetworkQualityMonitor::test_quality_assessment", "tests/test_speed_optimization.py::TestNetworkQualityMonitor::test_quality_score", "tests/test_speed_optimization.py::TestNetworkQualityMonitor::test_start_stop_monitoring", "tests/test_speed_optimization.py::TestResilientClientSpeedOptimization::test_socket_optimization", "tests/test_speed_optimization.py::TestResilientClientSpeedOptimization::test_speed_optimization_configuration", "tests/test_speed_optimization.py::TestResilientClientSpeedOptimization::test_speed_tracking_integration", "tests/test_speed_optimization.py::TestSpeedController::test_configuration", "tests/test_speed_optimization.py::TestSpeedController::test_initialization", "tests/test_speed_optimization.py::TestSpeedController::test_parameter_calculation", "tests/test_speed_optimization.py::TestSpeedController::test_performance_updates", "tests/test_speed_optimization.py::TestSpeedController::test_throttle_delay_calculation", "tests/test_speed_optimization.py::TestSpeedController::test_throttling", "tests/test_speed_optimization.py::TestSpeedOptimizationIntegration::test_full_optimization_cycle", "tests/test_speed_optimization.py::TestSpeedOptimizationIntegration::test_quality_degradation_response", "tests/test_ui_colors.py::TestUIColors::test_button_styles", "tests/test_ui_colors.py::TestUIColors::test_color_accessibility", "tests/test_ui_colors.py::TestUIColors::test_color_definitions", "tests/test_ui_colors.py::TestUIColors::test_font_definitions", "tests/test_ui_colors.py::TestUIColors::test_label_styles", "tests/test_ui_colors.py::TestUIColors::test_text_contrast", "tests/test_ui_colors.py::TestUIColors::test_theme_application", "tests/test_ui_colors.py::TestUIColors::test_widget_creation_with_styles", "tests/test_ui_colors.py::TestUIIntegration::test_progress_dialog_colors", "tests/test_ui_colors.py::TestUIIntegration::test_settings_dialog_colors"]
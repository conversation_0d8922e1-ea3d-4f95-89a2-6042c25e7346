#!/usr/bin/env python3
"""
Demo script for the File Transfer Application.

This script demonstrates the core functionality of the file transfer application
by creating test files and performing transfers between a local server and client.
"""

import sys
import time
import tempfile
import threading
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.server import FileTransferServer
from src.core.client import FileTransferClient
from src.utils.network_utils import NetworkUtils
from src.utils.file_utils import FileUtils
from src.utils.logger import setup_logger


def create_test_files(temp_dir: Path) -> list:
    """Create test files for demonstration."""
    test_files = []
    
    # Create a small text file
    small_file = temp_dir / "small_test.txt"
    with open(small_file, 'w') as f:
        f.write("Hello, World!\n" * 100)
    test_files.append(str(small_file))
    
    # Create a medium binary file
    medium_file = temp_dir / "medium_test.bin"
    with open(medium_file, 'wb') as f:
        f.write(b"Binary data test\n" * 1000)
    test_files.append(str(medium_file))
    
    # Create a larger text file
    large_file = temp_dir / "large_test.txt"
    with open(large_file, 'w') as f:
        for i in range(10000):
            f.write(f"Line {i}: This is a test line for the large file demo.\n")
    test_files.append(str(large_file))
    
    return test_files


def demo_file_transfer():
    """Demonstrate file transfer functionality."""
    logger = setup_logger(level=20)  # INFO level
    
    print("🚀 File Transfer Application Demo")
    print("=" * 50)
    
    # Create temporary directories
    with tempfile.TemporaryDirectory() as send_dir, tempfile.TemporaryDirectory() as receive_dir:
        send_path = Path(send_dir)
        receive_path = Path(receive_dir)
        
        print(f"📁 Send directory: {send_path}")
        print(f"📁 Receive directory: {receive_path}")
        print()
        
        # Create test files
        print("📝 Creating test files...")
        test_files = create_test_files(send_path)
        
        for file_path in test_files:
            file_info = FileUtils.get_file_info(file_path)
            print(f"   ✓ {file_info['name']} ({file_info['size_human']})")
        print()
        
        # Find available port
        port = NetworkUtils.find_available_port(start_port=9000)
        print(f"🌐 Using port: {port}")
        print()
        
        # Setup server
        print("🖥️  Setting up server...")
        server = FileTransferServer(
            host="localhost",
            port=port,
            download_dir=str(receive_path),
            chunk_size=4096
        )
        
        # Track server events
        received_files = []
        
        def on_client_connected(client_id, client_address):
            print(f"   📱 Client connected: {client_id}")
        
        def on_file_received(filename, client_id):
            received_files.append(filename)
            print(f"   ✅ File received: {filename}")
        
        def on_transfer_progress(filename, progress, bytes_received, total_bytes, client_id):
            if int(progress) % 25 == 0:  # Show progress every 25%
                print(f"   📊 {filename}: {progress:.1f}% ({FileUtils.format_file_size(bytes_received)}/{FileUtils.format_file_size(total_bytes)})")
        
        server.on_client_connected = on_client_connected
        server.on_file_received = on_file_received
        server.on_transfer_progress = on_transfer_progress
        
        # Start server in background thread
        server_thread = threading.Thread(target=server.start, daemon=True)
        server_thread.start()
        
        # Wait for server to start
        time.sleep(0.5)
        print("   ✓ Server started and listening")
        print()
        
        try:
            # Setup client
            print("📱 Setting up client...")
            client = FileTransferClient(chunk_size=4096)
            
            # Track client events
            def on_transfer_progress_client(filename, progress, bytes_sent, total_bytes):
                if int(progress) % 25 == 0:  # Show progress every 25%
                    print(f"   📤 Sending {filename}: {progress:.1f}%")
            
            def on_transfer_complete(filename, success):
                if success:
                    print(f"   ✅ Transfer completed: {filename}")
                else:
                    print(f"   ❌ Transfer failed: {filename}")
            
            client.on_transfer_progress = on_transfer_progress_client
            client.on_transfer_complete = on_transfer_complete
            
            # Connect to server
            print(f"   🔗 Connecting to localhost:{port}...")
            if not client.connect("localhost", port):
                print("   ❌ Failed to connect to server")
                return
            
            print("   ✓ Connected to server")
            print()
            
            # Transfer files
            print("📤 Starting file transfers...")
            total_files = len(test_files)
            
            for i, file_path in enumerate(test_files, 1):
                filename = Path(file_path).name
                file_size = FileUtils.get_file_size(file_path)
                
                print(f"   📄 Transferring {filename} ({FileUtils.format_file_size(file_size)}) [{i}/{total_files}]")
                
                start_time = time.time()
                success = client.send_file(file_path)
                end_time = time.time()
                
                if success:
                    transfer_time = end_time - start_time
                    speed = file_size / transfer_time if transfer_time > 0 else 0
                    print(f"   ✅ Transfer completed in {transfer_time:.2f}s ({FileUtils.format_file_size(int(speed))}/s)")
                else:
                    print(f"   ❌ Transfer failed")
                
                print()
            
            # Disconnect client
            client.disconnect()
            print("   📱 Client disconnected")
            print()
            
            # Verify received files
            print("🔍 Verifying received files...")
            
            for original_file in test_files:
                original_path = Path(original_file)
                received_path = receive_path / original_path.name
                
                if received_path.exists():
                    original_size = FileUtils.get_file_size(str(original_path))
                    received_size = FileUtils.get_file_size(str(received_path))
                    
                    if original_size == received_size:
                        # Verify content
                        original_checksum = FileUtils.calculate_md5(str(original_path))
                        received_checksum = FileUtils.calculate_md5(str(received_path))
                        
                        if original_checksum == received_checksum:
                            print(f"   ✅ {original_path.name}: Size and content match")
                        else:
                            print(f"   ❌ {original_path.name}: Content mismatch")
                    else:
                        print(f"   ❌ {original_path.name}: Size mismatch ({original_size} vs {received_size})")
                else:
                    print(f"   ❌ {original_path.name}: File not received")
            
            print()
            
            # Summary
            print("📊 Transfer Summary:")
            print(f"   Files sent: {len(test_files)}")
            print(f"   Files received: {len(received_files)}")
            
            total_size = sum(FileUtils.get_file_size(f) for f in test_files)
            print(f"   Total data transferred: {FileUtils.format_file_size(total_size)}")
            
            if len(received_files) == len(test_files):
                print("   🎉 All transfers completed successfully!")
            else:
                print("   ⚠️  Some transfers failed")
            
        finally:
            # Stop server
            server.stop()
            print("\n🖥️  Server stopped")
    
    print("\n✨ Demo completed!")


def main():
    """Main demo function."""
    try:
        demo_file_transfer()
    except KeyboardInterrupt:
        print("\n\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

"""
Tests for core file transfer functionality.
"""

import pytest
import tempfile
import threading
import time
import os
from pathlib import Path
import sys

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.core.server import FileTransferServer
from src.core.client import FileTransferClient
from src.core.protocol import FileTransferProtocol, MessageType, FileMetadata
from src.core.encryption import EncryptionManager
from src.utils.file_utils import FileUtils
from src.utils.network_utils import NetworkUtils


class TestFileUtils:
    """Test file utility functions."""
    
    def test_format_file_size(self):
        """Test file size formatting."""
        assert FileUtils.format_file_size(0) == "0 B"
        assert FileUtils.format_file_size(1024) == "1.0 KB"
        assert FileUtils.format_file_size(1024 * 1024) == "1.0 MB"
        assert FileUtils.format_file_size(1024 * 1024 * 1024) == "1.0 GB"
    
    def test_is_safe_filename(self):
        """Test filename safety checks."""
        assert FileUtils.is_safe_filename("test.txt") == True
        assert FileUtils.is_safe_filename("../test.txt") == False
        assert FileUtils.is_safe_filename("test/file.txt") == False
        assert FileUtils.is_safe_filename("CON") == False
        assert FileUtils.is_safe_filename("test<file>.txt") == False
    
    def test_sanitize_filename(self):
        """Test filename sanitization."""
        assert FileUtils.sanitize_filename("test.txt") == "test.txt"
        assert FileUtils.sanitize_filename("test<file>.txt") == "test_file_.txt"
        assert FileUtils.sanitize_filename("../test.txt") == "test.txt"  # basename removes path
        assert FileUtils.sanitize_filename("test|file.txt") == "test_file.txt"
    
    def test_calculate_md5(self):
        """Test MD5 calculation."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write("Hello, World!")
            temp_path = f.name
        
        try:
            md5_hash = FileUtils.calculate_md5(temp_path)
            assert len(md5_hash) == 32
            assert md5_hash == "65a8e27d8879283831b664bd8b7f0ad4"
        finally:
            os.unlink(temp_path)


class TestNetworkUtils:
    """Test network utility functions."""
    
    def test_is_valid_ip(self):
        """Test IP address validation."""
        assert NetworkUtils.is_valid_ip("127.0.0.1") == True
        assert NetworkUtils.is_valid_ip("***********") == True
        assert NetworkUtils.is_valid_ip("invalid") == False
        assert NetworkUtils.is_valid_ip("256.1.1.1") == False
    
    def test_is_valid_port(self):
        """Test port validation."""
        assert NetworkUtils.is_valid_port(80) == True
        assert NetworkUtils.is_valid_port(8888) == True
        assert NetworkUtils.is_valid_port(0) == False
        assert NetworkUtils.is_valid_port(65536) == False
    
    def test_find_available_port(self):
        """Test finding available ports."""
        port = NetworkUtils.find_available_port(start_port=9000, max_attempts=10)
        assert port is not None
        assert 9000 <= port < 9010


class TestFileTransferProtocol:
    """Test file transfer protocol."""
    
    def test_create_and_parse_message(self):
        """Test message creation and parsing."""
        payload = {"test": "data", "number": 42}
        message_bytes = FileTransferProtocol.create_message(MessageType.HEARTBEAT, payload)
        
        parsed = FileTransferProtocol.parse_message(message_bytes)
        assert parsed is not None
        assert parsed["type"] == MessageType.HEARTBEAT.value
        assert parsed["payload"] == payload
    
    def test_file_metadata(self):
        """Test file metadata handling."""
        metadata = FileMetadata(
            filename="test.txt",
            size=1024,
            checksum="abc123",
            chunk_size=512
        )
        
        assert metadata.total_chunks == 2
        
        # Test serialization
        data = metadata.to_dict()
        metadata2 = FileMetadata.from_dict(data)
        
        assert metadata2.filename == metadata.filename
        assert metadata2.size == metadata.size
        assert metadata2.checksum == metadata.checksum
        assert metadata2.total_chunks == metadata.total_chunks


class TestEncryption:
    """Test encryption functionality."""
    
    def test_aes_encryption(self):
        """Test AES encryption and decryption."""
        manager = EncryptionManager()
        
        # Generate key and test data
        key = manager.generate_aes_key()
        test_data = b"Hello, World! This is a test message."
        
        # Encrypt
        encrypted_data, nonce = manager.encrypt_data(test_data, key)
        assert encrypted_data != test_data
        
        # Decrypt
        decrypted_data = manager.decrypt_data(encrypted_data, key, nonce)
        assert decrypted_data == test_data
    
    def test_rsa_key_exchange(self):
        """Test RSA key exchange."""
        manager1 = EncryptionManager()
        manager2 = EncryptionManager()
        
        # Generate AES key
        aes_key = manager1.generate_aes_key()
        
        # Get public key from manager2
        public_key_pem = manager2.get_public_key_pem()
        
        # Encrypt AES key with manager2's public key
        encrypted_key = manager1.encrypt_aes_key(aes_key, public_key_pem)
        assert encrypted_key is not None
        
        # Decrypt with manager2's private key
        decrypted_key = manager2.decrypt_aes_key(encrypted_key)
        assert decrypted_key == aes_key


class TestFileTransfer:
    """Test file transfer functionality."""
    
    @pytest.fixture
    def temp_file(self):
        """Create a temporary file for testing."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write("This is a test file for file transfer testing.\n" * 100)
            temp_path = f.name
        
        yield temp_path
        
        # Cleanup
        try:
            os.unlink(temp_path)
        except:
            pass
    
    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for testing."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        
        # Cleanup
        import shutil
        try:
            shutil.rmtree(temp_dir)
        except:
            pass
    
    def test_server_client_connection(self, temp_dir):
        """Test basic server-client connection."""
        # Find available port
        port = NetworkUtils.find_available_port(start_port=9000)
        assert port is not None
        
        # Create server
        server = FileTransferServer(
            host="localhost",
            port=port,
            download_dir=temp_dir,
            chunk_size=1024
        )
        
        # Start server in thread
        server_thread = threading.Thread(target=server.start, daemon=True)
        server_thread.start()
        
        # Wait for server to start
        time.sleep(0.5)
        
        try:
            # Create client and connect
            client = FileTransferClient(chunk_size=1024)
            assert client.connect("localhost", port) == True
            assert client.is_connected() == True
            
            # Disconnect
            client.disconnect()
            assert client.is_connected() == False
            
        finally:
            # Stop server
            server.stop()
    
    def test_file_transfer(self, temp_file, temp_dir):
        """Test actual file transfer."""
        # Find available port
        port = NetworkUtils.find_available_port(start_port=9001)
        assert port is not None
        
        # Create server
        server = FileTransferServer(
            host="localhost",
            port=port,
            download_dir=temp_dir,
            chunk_size=512
        )
        
        # Track received files
        received_files = []
        
        def on_file_received(filename, client_id):
            received_files.append(filename)
        
        server.on_file_received = on_file_received
        
        # Start server in thread
        server_thread = threading.Thread(target=server.start, daemon=True)
        server_thread.start()
        
        # Wait for server to start
        time.sleep(0.5)
        
        try:
            # Create client and connect
            client = FileTransferClient(chunk_size=512)
            assert client.connect("localhost", port) == True
            
            # Send file
            success = client.send_file(temp_file)
            assert success == True
            
            # Wait for transfer to complete
            time.sleep(1.0)
            
            # Check if file was received
            assert len(received_files) == 1
            
            # Check if file exists in download directory
            filename = Path(temp_file).name
            downloaded_file = Path(temp_dir) / filename
            assert downloaded_file.exists()
            
            # Verify file content
            with open(temp_file, 'r') as f1, open(downloaded_file, 'r') as f2:
                assert f1.read() == f2.read()
            
            client.disconnect()
            
        finally:
            # Stop server
            server.stop()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

"""
Modern connection manager with support for DDNS, hostnames, and connection history.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import socket
from pathlib import Path
from typing import Dict, List, Optional, Callable
from src.utils.network_utils import NetworkUtils
from src.gui.theme import ModernTheme, IconManager


class ConnectionManager:
    """
    Manages connection profiles and history for easy peer-to-peer connections.
    """
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        Initialize the connection manager.
        
        Args:
            config_dir: Directory to store configuration files
        """
        if config_dir is None:
            config_dir = Path.home() / ".filetransfer"
        
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        self.connections_file = self.config_dir / "connections.json"
        self.history_file = self.config_dir / "history.json"
        
        self.connections: List[Dict] = self._load_connections()
        self.history: List[Dict] = self._load_history()
    
    def _load_connections(self) -> List[Dict]:
        """Load saved connections."""
        try:
            if self.connections_file.exists():
                with open(self.connections_file, 'r') as f:
                    return json.load(f)
        except Exception:
            pass
        return []
    
    def _save_connections(self):
        """Save connections to file."""
        try:
            with open(self.connections_file, 'w') as f:
                json.dump(self.connections, f, indent=2)
        except Exception:
            pass
    
    def _load_history(self) -> List[Dict]:
        """Load connection history."""
        try:
            if self.history_file.exists():
                with open(self.history_file, 'r') as f:
                    return json.load(f)
        except Exception:
            pass
        return []
    
    def _save_history(self):
        """Save connection history to file."""
        try:
            with open(self.history_file, 'w') as f:
                json.dump(self.history[-50:], f, indent=2)  # Keep last 50 entries
        except Exception:
            pass
    
    def add_connection(self, name: str, host: str, port: int, description: str = "") -> bool:
        """
        Add a new connection profile.
        
        Args:
            name: Connection name
            host: Host address (IP, hostname, or DDNS)
            port: Port number
            description: Optional description
            
        Returns:
            True if added successfully
        """
        # Check if name already exists
        if any(conn['name'] == name for conn in self.connections):
            return False
        
        connection = {
            'name': name,
            'host': host,
            'port': port,
            'description': description,
            'created': self._get_timestamp()
        }
        
        self.connections.append(connection)
        self._save_connections()
        return True
    
    def remove_connection(self, name: str) -> bool:
        """Remove a connection profile."""
        self.connections = [conn for conn in self.connections if conn['name'] != name]
        self._save_connections()
        return True
    
    def get_connections(self) -> List[Dict]:
        """Get all saved connections."""
        return self.connections.copy()
    
    def add_to_history(self, host: str, port: int, success: bool, direction: str):
        """
        Add a connection attempt to history.
        
        Args:
            host: Host address
            port: Port number
            success: Whether connection was successful
            direction: 'outgoing' or 'incoming'
        """
        entry = {
            'host': host,
            'port': port,
            'success': success,
            'direction': direction,
            'timestamp': self._get_timestamp()
        }
        
        self.history.append(entry)
        self._save_history()
    
    def get_recent_connections(self, limit: int = 10) -> List[Dict]:
        """Get recent successful connections."""
        successful = [h for h in self.history if h['success']]
        return successful[-limit:]
    
    def _get_timestamp(self) -> str:
        """Get current timestamp."""
        import datetime
        return datetime.datetime.now().isoformat()
    
    def resolve_host(self, host: str) -> Optional[str]:
        """
        Resolve hostname/DDNS to IP address.
        
        Args:
            host: Hostname, DDNS, or IP address
            
        Returns:
            Resolved IP address or None if failed
        """
        # If it's already an IP address, return as-is
        if NetworkUtils.is_valid_ip(host):
            return host
        
        # Try to resolve hostname/DDNS
        try:
            return socket.gethostbyname(host)
        except socket.gaierror:
            return None


class ConnectionDialog:
    """
    Modern dialog for managing connections.
    """
    
    def __init__(self, parent: tk.Tk, connection_manager: ConnectionManager):
        """
        Initialize the connection dialog.
        
        Args:
            parent: Parent window
            connection_manager: Connection manager instance
        """
        self.parent = parent
        self.connection_manager = connection_manager
        self.result = None
        
        # Create dialog
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Connection Manager")
        self.dialog.geometry("600x500")
        self.dialog.resizable(True, True)
        
        # Make dialog modal
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Apply theme
        self.style = ModernTheme.apply_theme(self.dialog)
        
        # Center dialog
        self._center_dialog()
        
        # Create widgets
        self._create_widgets()
        
        # Handle dialog close
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_cancel)
    
    def _create_widgets(self):
        """Create and layout dialog widgets."""
        main_frame = ttk.Frame(self.dialog, padding=ModernTheme.SPACING['lg'])
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, ModernTheme.SPACING['lg']))
        
        title_label = ttk.Label(
            title_frame,
            text=f"{IconManager.get_icon('network')} Connection Manager",
            style='Heading.TLabel'
        )
        title_label.pack(side=tk.LEFT)
        
        # Create notebook for tabs
        notebook = ttk.Notebook(main_frame, style='Modern.TNotebook')
        notebook.pack(fill=tk.BOTH, expand=True, pady=(0, ModernTheme.SPACING['lg']))
        
        # Quick Connect tab
        self._create_quick_connect_tab(notebook)
        
        # Saved Connections tab
        self._create_saved_connections_tab(notebook)
        
        # History tab
        self._create_history_tab(notebook)
        
        # Buttons
        self._create_buttons(main_frame)
    
    def _create_quick_connect_tab(self, notebook: ttk.Notebook):
        """Create the quick connect tab."""
        frame = ttk.Frame(notebook, padding=ModernTheme.SPACING['md'])
        notebook.add(frame, text=f"{IconManager.get_icon('connect')} Quick Connect")
        
        # Connection form
        form_frame = ttk.LabelFrame(frame, text="Connection Details", padding=ModernTheme.SPACING['md'])
        form_frame.pack(fill=tk.X, pady=(0, ModernTheme.SPACING['md']))
        
        # Host/IP
        ttk.Label(form_frame, text="Host/IP Address:").grid(row=0, column=0, sticky=tk.W, pady=(0, ModernTheme.SPACING['sm']))
        self.host_var = tk.StringVar()
        host_entry = ttk.Entry(form_frame, textvariable=self.host_var, style='Modern.TEntry', width=40)
        host_entry.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, ModernTheme.SPACING['md']))
        
        # Port
        ttk.Label(form_frame, text="Port:").grid(row=2, column=0, sticky=tk.W, pady=(0, ModernTheme.SPACING['sm']))
        self.port_var = tk.StringVar(value="8888")
        port_entry = ttk.Entry(form_frame, textvariable=self.port_var, style='Modern.TEntry', width=10)
        port_entry.grid(row=3, column=0, sticky=tk.W, pady=(0, ModernTheme.SPACING['md']))
        
        # Test connection button
        test_btn = ttk.Button(
            form_frame,
            text=f"{IconManager.get_icon('network')} Test Connection",
            command=self._test_connection,
            style='TestAction.TButton'
        )
        test_btn.grid(row=3, column=1, sticky=tk.E, padx=(ModernTheme.SPACING['md'], 0))
        
        # Connection status
        self.status_var = tk.StringVar(value="Ready to connect")
        status_label = ttk.Label(form_frame, textvariable=self.status_var, style='Muted.TLabel')
        status_label.grid(row=4, column=0, columnspan=2, sticky=tk.W, pady=(ModernTheme.SPACING['sm'], 0))
        
        # Save connection option
        save_frame = ttk.Frame(frame)
        save_frame.pack(fill=tk.X, pady=(0, ModernTheme.SPACING['md']))
        
        self.save_connection_var = tk.BooleanVar()
        save_check = ttk.Checkbutton(
            save_frame,
            text="Save this connection",
            variable=self.save_connection_var
        )
        save_check.pack(side=tk.LEFT)
        
        # Connection name (shown when save is checked)
        self.name_frame = ttk.Frame(save_frame)
        
        ttk.Label(self.name_frame, text="Name:").pack(side=tk.LEFT, padx=(ModernTheme.SPACING['md'], ModernTheme.SPACING['sm']))
        self.connection_name_var = tk.StringVar()
        name_entry = ttk.Entry(self.name_frame, textvariable=self.connection_name_var, style='Modern.TEntry', width=20)
        name_entry.pack(side=tk.LEFT)
        
        # Bind save checkbox
        save_check.configure(command=self._toggle_save_fields)
        
        # Configure grid weights
        form_frame.columnconfigure(0, weight=1)
    
    def _create_saved_connections_tab(self, notebook: ttk.Notebook):
        """Create the saved connections tab."""
        frame = ttk.Frame(notebook, padding=ModernTheme.SPACING['md'])
        notebook.add(frame, text=f"{IconManager.get_icon('folder')} Saved")
        
        # Connections list
        list_frame = ttk.Frame(frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, ModernTheme.SPACING['md']))
        
        # Create treeview for connections
        self.connections_tree = ttk.Treeview(
            list_frame,
            columns=('host', 'port', 'description'),
            show='tree headings',
            style='Modern.Treeview'
        )
        
        self.connections_tree.heading('#0', text='Name')
        self.connections_tree.heading('host', text='Host')
        self.connections_tree.heading('port', text='Port')
        self.connections_tree.heading('description', text='Description')
        
        self.connections_tree.column('#0', width=150)
        self.connections_tree.column('host', width=200)
        self.connections_tree.column('port', width=80)
        self.connections_tree.column('description', width=150)
        
        self.connections_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Scrollbar
        conn_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.connections_tree.yview)
        conn_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.connections_tree.configure(yscrollcommand=conn_scrollbar.set)
        
        # Buttons for saved connections
        conn_buttons = ttk.Frame(frame)
        conn_buttons.pack(fill=tk.X)
        
        ttk.Button(
            conn_buttons,
            text=f"{IconManager.get_icon('connect')} Connect",
            command=self._connect_saved,
            style='ConnectAction.TButton'
        ).pack(side=tk.LEFT, padx=(0, ModernTheme.SPACING['sm']))

        ttk.Button(
            conn_buttons,
            text=f"{IconManager.get_icon('remove')} Delete",
            command=self._delete_saved,
            style='DangerAction.TButton'
        ).pack(side=tk.LEFT)
        
        # Load saved connections
        self._refresh_connections()
    
    def _create_history_tab(self, notebook: ttk.Notebook):
        """Create the connection history tab."""
        frame = ttk.Frame(notebook, padding=ModernTheme.SPACING['md'])
        notebook.add(frame, text=f"{IconManager.get_icon('refresh')} History")
        
        # History list
        history_frame = ttk.Frame(frame)
        history_frame.pack(fill=tk.BOTH, expand=True)
        
        self.history_tree = ttk.Treeview(
            history_frame,
            columns=('host', 'port', 'status', 'direction', 'time'),
            show='headings',
            style='Modern.Treeview'
        )
        
        self.history_tree.heading('host', text='Host')
        self.history_tree.heading('port', text='Port')
        self.history_tree.heading('status', text='Status')
        self.history_tree.heading('direction', text='Direction')
        self.history_tree.heading('time', text='Time')
        
        self.history_tree.column('host', width=200)
        self.history_tree.column('port', width=80)
        self.history_tree.column('status', width=100)
        self.history_tree.column('direction', width=100)
        self.history_tree.column('time', width=150)
        
        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Scrollbar
        history_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.history_tree.yview)
        history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.history_tree.configure(yscrollcommand=history_scrollbar.set)
        
        # Load history
        self._refresh_history()
    
    def _create_buttons(self, parent):
        """Create dialog buttons."""
        button_frame = ttk.Frame(parent, padding="15")
        button_frame.pack(fill=tk.X, pady=(20, 0))

        # Cancel button with proper styling
        cancel_btn = ttk.Button(
            button_frame,
            text="Cancel",
            command=self._on_cancel,
            style='Secondary.TButton'
        )
        cancel_btn.pack(side=tk.RIGHT, padx=(ModernTheme.SPACING['md'], 0))

        # Connect button with proper styling and black text
        connect_btn = ttk.Button(
            button_frame,
            text=f"{IconManager.get_icon('connect')} Connect",
            command=self._on_connect,
            style='ConnectAction.TButton'
        )
        connect_btn.pack(side=tk.RIGHT, padx=(ModernTheme.SPACING['md'], 0))

    def _center_dialog(self):
        """Center the dialog on the parent window."""
        self.dialog.update_idletasks()

        # Get parent window position and size
        parent_x = self.parent.winfo_x()
        parent_y = self.parent.winfo_y()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()

        # Get dialog size
        dialog_width = self.dialog.winfo_width()
        dialog_height = self.dialog.winfo_height()

        # Calculate center position
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2

        self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")

    def _toggle_save_fields(self):
        """Toggle visibility of save connection fields."""
        if self.save_connection_var.get():
            self.name_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        else:
            self.name_frame.pack_forget()

    def _test_connection(self):
        """Test the connection."""
        host = self.host_var.get().strip()
        port_str = self.port_var.get().strip()

        if not host:
            self.status_var.set("❌ Please enter a host address")
            return

        try:
            port = int(port_str)
            if not NetworkUtils.is_valid_port(port):
                self.status_var.set("❌ Invalid port number")
                return
        except ValueError:
            self.status_var.set("❌ Invalid port number")
            return

        self.status_var.set("🔄 Testing connection...")
        self.dialog.update()

        # Resolve hostname if needed
        resolved_ip = self.connection_manager.resolve_host(host)
        if resolved_ip is None:
            self.status_var.set("❌ Cannot resolve hostname")
            return

        # Test connection
        if NetworkUtils.test_connection(resolved_ip, port, timeout=5):
            self.status_var.set(f"✅ Connection successful ({resolved_ip}:{port})")
        else:
            self.status_var.set(f"❌ Connection failed ({resolved_ip}:{port})")

    def _refresh_connections(self):
        """Refresh the saved connections list."""
        # Clear existing items
        for item in self.connections_tree.get_children():
            self.connections_tree.delete(item)

        # Add connections
        for conn in self.connection_manager.get_connections():
            self.connections_tree.insert(
                '',
                'end',
                text=conn['name'],
                values=(conn['host'], conn['port'], conn.get('description', ''))
            )

    def _refresh_history(self):
        """Refresh the connection history list."""
        # Clear existing items
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)

        # Add history entries
        for entry in reversed(self.connection_manager.history[-20:]):  # Last 20 entries
            status = "✅ Success" if entry['success'] else "❌ Failed"
            direction = "📤 Outgoing" if entry['direction'] == 'outgoing' else "📥 Incoming"

            # Format timestamp
            try:
                import datetime
                dt = datetime.datetime.fromisoformat(entry['timestamp'])
                time_str = dt.strftime("%Y-%m-%d %H:%M")
            except:
                time_str = entry['timestamp']

            self.history_tree.insert(
                '',
                'end',
                values=(entry['host'], entry['port'], status, direction, time_str)
            )

    def _connect_saved(self):
        """Connect using selected saved connection."""
        selection = self.connections_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a connection to connect to.")
            return

        item = self.connections_tree.item(selection[0])
        host = item['values'][0]
        port = int(item['values'][1])

        self.host_var.set(host)
        self.port_var.set(str(port))
        self._on_connect()

    def _delete_saved(self):
        """Delete selected saved connection."""
        selection = self.connections_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a connection to delete.")
            return

        item = self.connections_tree.item(selection[0])
        name = item['text']

        if messagebox.askyesno("Confirm Delete", f"Delete connection '{name}'?"):
            self.connection_manager.remove_connection(name)
            self._refresh_connections()

    def _on_connect(self):
        """Handle connect button click."""
        host = self.host_var.get().strip()
        port_str = self.port_var.get().strip()

        if not host:
            messagebox.showerror("Invalid Input", "Please enter a host address.")
            return

        try:
            port = int(port_str)
            if not NetworkUtils.is_valid_port(port):
                messagebox.showerror("Invalid Input", "Invalid port number (1-65535).")
                return
        except ValueError:
            messagebox.showerror("Invalid Input", "Invalid port number.")
            return

        # Save connection if requested
        if self.save_connection_var.get():
            name = self.connection_name_var.get().strip()
            if not name:
                messagebox.showerror("Invalid Input", "Please enter a connection name.")
                return

            if not self.connection_manager.add_connection(name, host, port):
                messagebox.showerror("Error", "A connection with this name already exists.")
                return

        # Return connection details
        self.result = {
            'host': host,
            'port': port
        }
        self.dialog.destroy()

    def _on_cancel(self):
        """Handle cancel button click."""
        self.result = None
        self.dialog.destroy()

    def show(self) -> Optional[Dict]:
        """
        Show the dialog and return connection details.

        Returns:
            Dictionary with host and port, or None if cancelled
        """
        self.dialog.wait_window()
        return self.result

#!/usr/bin/env python3
"""
Enhanced demo showcasing the modern UI and improved features.
"""

import sys
import time
import tempfile
import threading
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.server import FileTransferServer
from src.core.enhanced_client import EnhancedFileTransferClient
from src.utils.network_utils import NetworkUtils
from src.utils.file_utils import FileUtils
from src.utils.logger import setup_logger
from src.gui.connection_manager import ConnectionManager


def create_demo_files(temp_dir: Path) -> list:
    """Create demo files with different sizes and types."""
    test_files = []
    
    # Small text file
    small_file = temp_dir / "document.txt"
    with open(small_file, 'w', encoding='utf-8') as f:
        f.write("📄 File Transfer Pro Demo\n" * 50)
        f.write("\n🚀 Features:\n")
        f.write("• Modern professional UI\n")
        f.write("• Enhanced error handling\n")
        f.write("• Connection management\n")
        f.write("• DDNS/hostname support\n")
        f.write("• Progress tracking\n")
        f.write("• Retry logic\n")
        f.write("• File validation\n")
    test_files.append(str(small_file))
    
    # Medium binary file
    medium_file = temp_dir / "data.bin"
    with open(medium_file, 'wb') as f:
        # Create some binary data
        for i in range(5000):
            f.write(f"Binary data chunk {i:04d}\n".encode('utf-8'))
    test_files.append(str(medium_file))
    
    # Large text file
    large_file = temp_dir / "large_document.txt"
    with open(large_file, 'w', encoding='utf-8') as f:
        f.write("📊 Large File Transfer Test\n")
        f.write("=" * 50 + "\n\n")
        
        for section in range(100):
            f.write(f"Section {section + 1}\n")
            f.write("-" * 20 + "\n")
            
            for line in range(100):
                f.write(f"Line {line + 1}: This is a test line in section {section + 1}. ")
                f.write("It contains some sample text to demonstrate large file transfers. ")
                f.write("The enhanced client provides better progress tracking and error handling.\n")
            
            f.write("\n")
    test_files.append(str(large_file))
    
    return test_files


def demo_connection_manager():
    """Demonstrate the connection manager features."""
    print("🔗 Connection Manager Demo")
    print("-" * 30)
    
    # Create connection manager
    conn_mgr = ConnectionManager()
    
    # Add some demo connections
    connections = [
        ("Home Server", "*************", 8888, "Main home server"),
        ("Office PC", "office.example.com", 9999, "Work computer"),
        ("Friend's Laptop", "friend.ddns.net", 8888, "John's laptop"),
        ("Cloud Server", "myserver.example.org", 8080, "VPS in the cloud")
    ]
    
    print("📝 Adding demo connections...")
    for name, host, port, desc in connections:
        success = conn_mgr.add_connection(name, host, port, desc)
        if success:
            print(f"   ✓ Added: {name} ({host}:{port})")
        else:
            print(f"   ❌ Failed to add: {name}")
    
    print(f"\n📋 Saved connections: {len(conn_mgr.get_connections())}")
    for conn in conn_mgr.get_connections():
        print(f"   • {conn['name']}: {conn['host']}:{conn['port']}")
        if conn['description']:
            print(f"     {conn['description']}")
    
    # Add some demo history
    print("\n📈 Adding demo connection history...")
    history_entries = [
        ("*************", 8888, True, "outgoing"),
        ("************", 8888, False, "outgoing"),
        ("********", 9999, True, "incoming"),
        ("friend.ddns.net", 8888, True, "outgoing"),
    ]
    
    for host, port, success, direction in history_entries:
        conn_mgr.add_to_history(host, port, success, direction)
        status = "✅ Success" if success else "❌ Failed"
        arrow = "📤" if direction == "outgoing" else "📥"
        print(f"   {arrow} {host}:{port} - {status}")
    
    print(f"\n📊 Recent connections: {len(conn_mgr.get_recent_connections())}")
    
    return conn_mgr


def demo_enhanced_client():
    """Demonstrate enhanced client features."""
    print("\n🚀 Enhanced Client Demo")
    print("-" * 25)
    
    # Create enhanced client
    client = EnhancedFileTransferClient(
        chunk_size=4096,
        max_retries=3,
        retry_delay=0.5
    )
    
    # Setup callbacks
    def on_retry(attempt, max_attempts):
        print(f"   🔄 Retry attempt {attempt}/{max_attempts}")
    
    def on_progress(filename, progress, bytes_sent, total_bytes):
        if int(progress) % 20 == 0:  # Show every 20%
            print(f"   📊 {filename}: {progress:.1f}% ({FileUtils.format_file_size(bytes_sent)}/{FileUtils.format_file_size(total_bytes)})")
    
    client.on_retry_attempt = on_retry
    client.on_transfer_progress = on_progress
    
    print("✓ Enhanced client created with:")
    print(f"   • Chunk size: {FileUtils.format_file_size(client.chunk_size)}")
    print(f"   • Max retries: {client.max_retries}")
    print(f"   • Retry delay: {client.retry_delay}s")
    
    # Test hostname resolution
    print("\n🌐 Testing hostname resolution...")
    test_hosts = ["localhost", "127.0.0.1", "invalid.hostname.test"]
    
    for host in test_hosts:
        resolved = client._resolve_host(host)
        if resolved:
            print(f"   ✓ {host} → {resolved}")
        else:
            print(f"   ❌ {host} → Failed to resolve")
    
    return client


def demo_file_validation():
    """Demonstrate file validation features."""
    print("\n🔍 File Validation Demo")
    print("-" * 23)
    
    # Create temp files for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Valid file
        valid_file = temp_path / "valid.txt"
        with open(valid_file, 'w') as f:
            f.write("This is a valid file for testing.")
        
        # Empty file
        empty_file = temp_path / "empty.txt"
        empty_file.touch()
        
        # Test validation
        from src.utils.error_handler import Validator
        
        test_cases = [
            (str(valid_file), "Valid file"),
            (str(empty_file), "Empty file"),
            ("nonexistent.txt", "Non-existent file"),
            ("", "Empty path"),
            (str(temp_path), "Directory path")
        ]
        
        print("📋 File validation results:")
        for file_path, description in test_cases:
            is_valid = Validator.validate_file_path(file_path)
            status = "✅ Valid" if is_valid else "❌ Invalid"
            print(f"   {status}: {description}")
            
            if is_valid:
                size = FileUtils.get_file_size(file_path)
                print(f"      Size: {FileUtils.format_file_size(size)}")


def demo_error_handling():
    """Demonstrate error handling system."""
    print("\n⚠️  Error Handling Demo")
    print("-" * 22)
    
    from src.utils.error_handler import ErrorHandler, ErrorSeverity, ErrorCategory
    
    error_handler = ErrorHandler()
    
    # Simulate various errors
    test_errors = [
        ("NET_001", "Connection refused"),
        ("FS_001", "File not found"),
        ("SEC_002", "Checksum verification failed"),
        ("UI_001", "Required field is empty")
    ]
    
    print("🚨 Simulating error scenarios:")
    for error_code, description in test_errors:
        error_info = error_handler.handle_error(error_code, context={"demo": True})
        
        severity_icon = {
            ErrorSeverity.LOW: "ℹ️",
            ErrorSeverity.MEDIUM: "⚠️",
            ErrorSeverity.HIGH: "❌",
            ErrorSeverity.CRITICAL: "🚨"
        }
        
        icon = severity_icon.get(error_info.severity, "❓")
        print(f"   {icon} [{error_code}] {error_info.message}")
        print(f"      Category: {error_info.category.value}")
        print(f"      Severity: {error_info.severity.value}")
        
        if error_info.suggestions:
            print("      Suggestions:")
            for suggestion in error_info.suggestions[:2]:  # Show first 2 suggestions
                print(f"        • {suggestion}")
    
    print(f"\n📊 Error history: {len(error_handler.get_error_history())} entries")


def demo_network_features():
    """Demonstrate network utility features."""
    print("\n🌐 Network Features Demo")
    print("-" * 23)
    
    # Get network information
    local_ip = NetworkUtils.get_local_ip()
    all_ips = NetworkUtils.get_all_local_ips()
    hostname = NetworkUtils.get_hostname()
    
    print("🖥️  Network Information:")
    print(f"   • Hostname: {hostname}")
    print(f"   • Primary IP: {local_ip}")
    print(f"   • All IPs: {', '.join(all_ips)}")
    
    # Test port availability
    print("\n🔌 Port Availability:")
    test_ports = [8888, 9999, 80, 443, 22]
    
    for port in test_ports:
        available = NetworkUtils.is_port_available("localhost", port)
        status = "🟢 Available" if available else "🔴 In use"
        print(f"   Port {port}: {status}")
    
    # Find available port
    available_port = NetworkUtils.find_available_port(start_port=9000, max_attempts=10)
    if available_port:
        print(f"\n✅ Found available port: {available_port}")
    else:
        print("\n❌ No available ports found in range")


def main():
    """Main demo function."""
    print("🎉 File Transfer Pro - Enhanced Demo")
    print("=" * 45)
    print()
    
    # Setup logging
    logger = setup_logger(level=20)  # INFO level
    
    try:
        # Demo connection manager
        conn_mgr = demo_connection_manager()
        
        # Demo enhanced client
        client = demo_enhanced_client()
        
        # Demo file validation
        demo_file_validation()
        
        # Demo error handling
        demo_error_handling()
        
        # Demo network features
        demo_network_features()
        
        print("\n🎯 Key Improvements Summary:")
        print("=" * 35)
        print("✅ Modern professional UI with themes")
        print("✅ Enhanced connection management")
        print("✅ DDNS and hostname support")
        print("✅ Improved error handling and recovery")
        print("✅ File validation and safety checks")
        print("✅ Progress tracking and statistics")
        print("✅ Retry logic with exponential backoff")
        print("✅ Connection history and profiles")
        print("✅ Better network utilities")
        print("✅ Comprehensive logging")
        
        print("\n🚀 Ready for Distribution:")
        print("• Run 'python build_executable.py' to create standalone executable")
        print("• Share the portable package with friends")
        print("• Both computers need the app to transfer files")
        print("• Use the modern GUI for the best experience")
        
        print("\n✨ Demo completed successfully!")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

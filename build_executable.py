#!/usr/bin/env python3
"""
Build script to create standalone executables for easy distribution.
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path


def check_requirements():
    """Check if required tools are installed."""
    try:
        import PyInstaller
        print("✓ PyInstaller found")
    except ImportError:
        print("❌ PyInstaller not found. Installing...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller installed")


def clean_build_dirs():
    """Clean previous build directories."""
    dirs_to_clean = ["build", "dist", "__pycache__"]
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"🧹 Cleaning {dir_name}/")
            shutil.rmtree(dir_name)
    
    # Clean .spec files
    for spec_file in Path(".").glob("*.spec"):
        print(f"🧹 Removing {spec_file}")
        spec_file.unlink()


def create_executable():
    """Create standalone executable."""
    print("🔨 Building executable...")
    
    # PyInstaller command
    cmd = [
        "pyinstaller",
        "--onefile",                    # Single executable file
        "--windowed",                   # No console window (for GUI)
        "--name=FileTransferPro",       # Executable name
        "--icon=icon.ico",              # Icon file (if exists)
        "--add-data=src;src",           # Include source directory
        "--add-data=docs;docs",         # Include documentation
        "--hidden-import=tkinter",      # Ensure tkinter is included
        "--hidden-import=cryptography", # Ensure cryptography is included
        "--clean",                      # Clean cache
        "main.py"                       # Main script
    ]
    
    # Remove icon parameter if icon file doesn't exist
    if not os.path.exists("icon.ico"):
        cmd = [arg for arg in cmd if not arg.startswith("--icon")]
    
    try:
        subprocess.check_call(cmd)
        print("✅ Executable built successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
        return False


def create_portable_package():
    """Create a portable package with all necessary files."""
    print("📦 Creating portable package...")
    
    package_dir = Path("FileTransferPro_Portable")
    
    # Create package directory
    if package_dir.exists():
        shutil.rmtree(package_dir)
    package_dir.mkdir()
    
    # Copy executable
    exe_name = "FileTransferPro.exe" if sys.platform == "win32" else "FileTransferPro"
    exe_path = Path("dist") / exe_name
    
    if exe_path.exists():
        shutil.copy2(exe_path, package_dir / exe_name)
        print(f"✓ Copied {exe_name}")
    else:
        print(f"❌ Executable not found: {exe_path}")
        return False
    
    # Copy documentation
    docs_to_copy = [
        "README.md",
        "docs/user_guide.md",
        "requirements.txt"
    ]
    
    for doc in docs_to_copy:
        if os.path.exists(doc):
            dest = package_dir / Path(doc).name
            shutil.copy2(doc, dest)
            print(f"✓ Copied {doc}")
    
    # Create launcher scripts
    create_launcher_scripts(package_dir)
    
    # Create README for the package
    create_package_readme(package_dir)
    
    print(f"✅ Portable package created: {package_dir}/")
    return True


def create_launcher_scripts(package_dir: Path):
    """Create launcher scripts for different modes."""
    exe_name = "FileTransferPro.exe" if sys.platform == "win32" else "./FileTransferPro"
    
    # Windows batch files
    if sys.platform == "win32":
        # GUI launcher
        gui_launcher = package_dir / "Start_FileTransfer.bat"
        with open(gui_launcher, 'w') as f:
            f.write(f"""@echo off
title File Transfer Pro
echo Starting File Transfer Pro...
{exe_name}
pause
""")
        
        # Server launcher
        server_launcher = package_dir / "Start_Server.bat"
        with open(server_launcher, 'w') as f:
            f.write(f"""@echo off
title File Transfer Pro - Server Mode
echo Starting File Transfer Pro in Server Mode...
echo.
echo The server will start and listen for incoming connections.
echo Share your IP address with others to receive files.
echo.
{exe_name} --mode server
pause
""")
        
        print("✓ Created Windows launcher scripts")
    
    # Unix shell scripts
    else:
        # GUI launcher
        gui_launcher = package_dir / "start_filetransfer.sh"
        with open(gui_launcher, 'w') as f:
            f.write(f"""#!/bin/bash
echo "Starting File Transfer Pro..."
{exe_name}
""")
        gui_launcher.chmod(0o755)
        
        # Server launcher
        server_launcher = package_dir / "start_server.sh"
        with open(server_launcher, 'w') as f:
            f.write(f"""#!/bin/bash
echo "Starting File Transfer Pro in Server Mode..."
echo ""
echo "The server will start and listen for incoming connections."
echo "Share your IP address with others to receive files."
echo ""
{exe_name} --mode server
""")
        server_launcher.chmod(0o755)
        
        print("✓ Created Unix launcher scripts")


def create_package_readme(package_dir: Path):
    """Create README for the portable package."""
    readme_content = """# File Transfer Pro - Portable Package

## Quick Start

### For Windows:
- **Start GUI**: Double-click `Start_FileTransfer.bat`
- **Start Server**: Double-click `Start_Server.bat`

### For Linux/Mac:
- **Start GUI**: Run `./start_filetransfer.sh`
- **Start Server**: Run `./start_server.sh`

## Manual Usage

You can also run the executable directly:

```bash
# Start GUI (default)
./FileTransferPro

# Start as server
./FileTransferPro --mode server

# Start as server on specific port
./FileTransferPro --mode server --port 9999

# Show help
./FileTransferPro --help
```

## Features

- 🚀 **Fast TCP-based transfers** - Reliable file transfer with automatic chunking
- 🎨 **Modern GUI** - Professional interface with progress tracking
- 🔒 **Secure transfers** - Optional AES encryption
- 📁 **Multiple files** - Send multiple files and folders
- 🌐 **Cross-platform** - Works on Windows, Mac, and Linux
- 📱 **Easy sharing** - Simple connection management

## How to Use

### Sending Files:
1. Start the application
2. Click "Connect" to connect to a receiver
3. Add files or folders to send
4. Click "Send Files"

### Receiving Files:
1. Start the application
2. Switch to "Receive Files" mode
3. Click "Start Server"
4. Share your IP address with the sender

## Troubleshooting

- **Connection issues**: Check firewall settings and ensure both computers are on the same network
- **Port conflicts**: Try a different port number
- **Large files**: Be patient, large files take time to transfer

## Support

For help and documentation, see the included user guide or visit:
https://github.com/your-repo/FileTransferPro

---
File Transfer Pro - Secure, Fast, Easy File Sharing
"""
    
    readme_path = package_dir / "README.txt"
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✓ Created package README")


def create_installer():
    """Create an installer (Windows only)."""
    if sys.platform != "win32":
        print("ℹ️  Installer creation is only supported on Windows")
        return
    
    try:
        # Check if NSIS is available
        subprocess.check_call(["makensis", "/VERSION"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        print("✓ NSIS found, creating installer...")
        
        # Create NSIS script
        nsis_script = create_nsis_script()
        
        # Run NSIS
        subprocess.check_call(["makensis", nsis_script])
        print("✅ Installer created successfully!")
        
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("ℹ️  NSIS not found. Skipping installer creation.")
        print("   Download NSIS from https://nsis.sourceforge.io/ to create installers.")


def create_nsis_script():
    """Create NSIS installer script."""
    script_content = '''
!define APP_NAME "File Transfer Pro"
!define APP_VERSION "1.0.0"
!define APP_PUBLISHER "Your Name"
!define APP_EXE "FileTransferPro.exe"

Name "${APP_NAME}"
OutFile "FileTransferPro_Setup.exe"
InstallDir "$PROGRAMFILES\\${APP_NAME}"
RequestExecutionLevel admin

Page directory
Page instfiles

Section "Install"
    SetOutPath "$INSTDIR"
    File "dist\\${APP_EXE}"
    File "README.md"
    
    CreateDirectory "$SMPROGRAMS\\${APP_NAME}"
    CreateShortCut "$SMPROGRAMS\\${APP_NAME}\\${APP_NAME}.lnk" "$INSTDIR\\${APP_EXE}"
    CreateShortCut "$DESKTOP\\${APP_NAME}.lnk" "$INSTDIR\\${APP_EXE}"
    
    WriteUninstaller "$INSTDIR\\Uninstall.exe"
SectionEnd

Section "Uninstall"
    Delete "$INSTDIR\\${APP_EXE}"
    Delete "$INSTDIR\\README.md"
    Delete "$INSTDIR\\Uninstall.exe"
    RMDir "$INSTDIR"
    
    Delete "$SMPROGRAMS\\${APP_NAME}\\${APP_NAME}.lnk"
    RMDir "$SMPROGRAMS\\${APP_NAME}"
    Delete "$DESKTOP\\${APP_NAME}.lnk"
SectionEnd
'''
    
    script_path = "installer.nsi"
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    return script_path


def main():
    """Main build function."""
    print("🚀 File Transfer Pro - Build Script")
    print("=" * 40)
    
    # Check requirements
    check_requirements()
    
    # Clean previous builds
    clean_build_dirs()
    
    # Create executable
    if not create_executable():
        print("❌ Build failed!")
        return 1
    
    # Create portable package
    if not create_portable_package():
        print("❌ Package creation failed!")
        return 1
    
    # Create installer (Windows only)
    create_installer()
    
    print("\n🎉 Build completed successfully!")
    print("\nFiles created:")
    print(f"  📁 Portable package: FileTransferPro_Portable/")
    print(f"  💾 Executable: dist/FileTransferPro{'exe' if sys.platform == 'win32' else ''}")
    
    if sys.platform == "win32" and os.path.exists("FileTransferPro_Setup.exe"):
        print(f"  📦 Installer: FileTransferPro_Setup.exe")
    
    print("\n📋 Next steps:")
    print("  1. Test the executable in the dist/ folder")
    print("  2. Share the portable package with friends")
    print("  3. Both computers need to run the application to transfer files")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())

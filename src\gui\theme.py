"""
Modern theme and styling for the file transfer application.
"""

import tkinter as tk
from tkinter import ttk
import sys
from typing import Dict, Any


class ModernTheme:
    """
    Modern theme with professional colors and styling.
    """
    
    # Color palette - Modern professional theme with enhanced section colors
    COLORS = {
        # Primary colors
        'primary': '#2563eb',           # Blue
        'primary_hover': '#1d4ed8',     # Darker blue
        'primary_light': '#3b82f6',     # Light blue
        'primary_bg': '#eff6ff',        # Very light blue background

        # Secondary colors
        'secondary': '#64748b',         # Slate
        'secondary_hover': '#475569',   # Darker slate
        'secondary_light': '#94a3b8',   # Light slate
        'secondary_bg': '#f1f5f9',      # Light slate background

        # Success/Error colors - improved for better contrast
        'success': '#059669',           # Darker green for better contrast
        'success_hover': '#047857',     # Even darker green for hover
        'success_bg': '#ecfdf5',        # Light green background
        'warning': '#d97706',           # Darker amber for better contrast
        'warning_hover': '#b45309',     # Darker amber for hover
        'warning_bg': '#fffbeb',        # Light amber background
        'error': '#dc2626',             # Darker red for better contrast
        'error_hover': '#b91c1c',       # Darker red for hover
        'error_bg': '#fef2f2',          # Light red background
        'info': '#2563eb',              # Darker blue for better contrast
        'info_bg': '#eff6ff',           # Light blue background

        # Background colors - enhanced hierarchy
        'bg_primary': '#ffffff',        # White - main content
        'bg_secondary': '#f8fafc',      # Very light gray - secondary areas
        'bg_tertiary': '#f1f5f9',       # Light gray - tertiary areas
        'bg_quaternary': '#e2e8f0',     # Medium light gray - subtle sections
        'bg_dark': '#1e293b',           # Dark slate

        # Section-specific backgrounds for visual separation
        'bg_header': '#fafbfc',         # Header section background
        'bg_transfer': '#f0f9ff',       # Transfer section (light blue tint)
        'bg_server': '#f0fdf4',         # Server section (light green tint)
        'bg_settings': '#faf5ff',       # Settings section (light purple tint)
        'bg_status': '#fffbeb',         # Status section (light amber tint)
        'bg_card': '#ffffff',           # Card/panel background
        'bg_sidebar': '#f8fafc',        # Sidebar background

        # Text colors
        'text_primary': '#0f172a',      # Very dark slate
        'text_secondary': '#475569',    # Dark slate
        'text_muted': '#64748b',        # Medium slate (better contrast)
        'text_white': '#ffffff',        # White
        'text_accent': '#2563eb',       # Accent text color

        # Border colors
        'border_light': '#e2e8f0',      # Light border
        'border_medium': '#cbd5e1',     # Medium border
        'border_dark': '#64748b',       # Dark border
        'border_accent': '#2563eb',     # Accent border

        # Special colors
        'accent': '#8b5cf6',            # Purple
        'highlight': '#fef3c7',         # Light yellow
        'focus': '#3b82f6',             # Focus indicator
        'hover': '#f1f5f9',             # Hover state
    }
    
    # Font configuration
    FONTS = {
        'default': ('Segoe UI', 9),
        'heading': ('Segoe UI', 12, 'bold'),
        'subheading': ('Segoe UI', 10, 'bold'),
        'small': ('Segoe UI', 8),
        'monospace': ('Consolas', 9),
    }
    
    # Spacing and sizing
    SPACING = {
        'xs': 4,
        'sm': 8,
        'md': 16,
        'lg': 24,
        'xl': 32,
    }
    
    @classmethod
    def apply_theme(cls, root: tk.Tk):
        """Apply the modern theme to the application."""
        # Configure the style
        style = ttk.Style()
        
        # Use a modern theme as base
        if sys.platform == "win32":
            style.theme_use('vista')
        elif sys.platform == "darwin":
            style.theme_use('aqua')
        else:
            style.theme_use('clam')
        
        # Configure styles
        cls._configure_button_styles(style)
        cls._configure_frame_styles(style)
        cls._configure_label_styles(style)
        cls._configure_entry_styles(style)
        cls._configure_notebook_styles(style)
        cls._configure_progressbar_styles(style)
        cls._configure_treeview_styles(style)
        
        # Configure root window
        root.configure(bg=cls.COLORS['bg_primary'])
        
        return style

    @classmethod
    def create_scrollable_frame(cls, parent, style_name: str = 'TFrame'):
        """
        Create a scrollable frame with consistent styling.

        Args:
            parent: Parent widget
            style_name: Style to apply to the scrollable frame

        Returns:
            tuple: (canvas, scrollbar, scrollable_frame)
        """
        import tkinter as tk
        from tkinter import ttk

        # Create canvas with enhanced styling
        canvas = tk.Canvas(
            parent,
            highlightthickness=0,
            bg=cls.COLORS['bg_primary'],
            relief='flat',
            borderwidth=0
        )

        # Create scrollbar with modern styling
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)

        # Create scrollable frame
        scrollable_frame = ttk.Frame(canvas, style=style_name)

        # Configure scrolling
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Bind mouse wheel scrolling
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_to_mousewheel(event):
            canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_from_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")

        canvas.bind('<Enter>', _bind_to_mousewheel)
        canvas.bind('<Leave>', _unbind_from_mousewheel)

        return canvas, scrollbar, scrollable_frame
    
    @classmethod
    def _configure_button_styles(cls, style: ttk.Style):
        """Configure button styles with proper sizing and text colors."""
        # Default button style with improved sizing
        style.configure(
            'TButton',
            background=cls.COLORS['bg_secondary'],
            foreground=cls.COLORS['text_primary'],
            borderwidth=1,
            relief='solid',
            bordercolor=cls.COLORS['border_medium'],
            focuscolor='none',
            padding=(cls.SPACING['lg'], cls.SPACING['md']),  # Increased padding for better size
            font=cls.FONTS['default'],
            width=12  # Minimum width for consistency
        )
        style.map(
            'TButton',
            background=[('active', cls.COLORS['bg_tertiary']),
                       ('pressed', cls.COLORS['bg_tertiary'])],
            foreground=[('active', cls.COLORS['text_primary']),
                       ('pressed', cls.COLORS['text_primary'])]
        )

        # Primary button - larger and more prominent
        style.configure(
            'Primary.TButton',
            background=cls.COLORS['primary'],
            foreground=cls.COLORS['text_white'],
            borderwidth=0,
            focuscolor='none',
            padding=(cls.SPACING['lg'], cls.SPACING['md']),
            font=cls.FONTS['default'],
            width=14  # Slightly larger for primary actions
        )
        style.map(
            'Primary.TButton',
            background=[('active', cls.COLORS['primary_hover']),
                       ('pressed', cls.COLORS['primary_hover'])],
            foreground=[('active', cls.COLORS['text_white']),
                       ('pressed', cls.COLORS['text_white'])]
        )

        # Secondary button - consistent sizing
        style.configure(
            'Secondary.TButton',
            background=cls.COLORS['bg_secondary'],
            foreground=cls.COLORS['text_primary'],
            borderwidth=1,
            relief='solid',
            bordercolor=cls.COLORS['border_medium'],
            focuscolor='none',
            padding=(cls.SPACING['lg'], cls.SPACING['md']),
            font=cls.FONTS['default'],
            width=12
        )
        style.map(
            'Secondary.TButton',
            background=[('active', cls.COLORS['bg_tertiary']),
                       ('pressed', cls.COLORS['bg_tertiary'])],
            foreground=[('active', cls.COLORS['text_primary']),
                       ('pressed', cls.COLORS['text_primary'])]
        )

        # Success button - clear success indication
        style.configure(
            'Success.TButton',
            background=cls.COLORS['success'],
            foreground=cls.COLORS['text_white'],
            borderwidth=0,
            focuscolor='none',
            padding=(cls.SPACING['lg'], cls.SPACING['md']),
            font=cls.FONTS['default'],
            width=12
        )
        style.map(
            'Success.TButton',
            background=[('active', cls.COLORS['success_hover']),
                       ('pressed', cls.COLORS['success_hover'])],
            foreground=[('active', cls.COLORS['text_white']),
                       ('pressed', cls.COLORS['text_white'])]
        )

        # SuccessAction button - for primary success actions
        style.configure(
            'SuccessAction.TButton',
            background=cls.COLORS['success'],
            foreground=cls.COLORS['text_white'],
            borderwidth=0,
            focuscolor='none',
            padding=(cls.SPACING['lg'], cls.SPACING['md']),
            font=cls.FONTS['default'],
            width=14
        )
        style.map(
            'SuccessAction.TButton',
            background=[('active', cls.COLORS['success_hover']),
                       ('pressed', cls.COLORS['success_hover'])],
            foreground=[('active', cls.COLORS['text_white']),
                       ('pressed', cls.COLORS['text_white'])]
        )

        # Danger button - clear warning indication
        style.configure(
            'Danger.TButton',
            background=cls.COLORS['error'],
            foreground=cls.COLORS['text_white'],
            borderwidth=0,
            focuscolor='none',
            padding=(cls.SPACING['lg'], cls.SPACING['md']),
            font=cls.FONTS['default'],
            width=12
        )
        style.map(
            'Danger.TButton',
            background=[('active', cls.COLORS['error_hover']),
                       ('pressed', cls.COLORS['error_hover'])],
            foreground=[('active', cls.COLORS['text_white']),
                       ('pressed', cls.COLORS['text_white'])]
        )

        # DangerAction button - for primary danger actions
        style.configure(
            'DangerAction.TButton',
            background=cls.COLORS['error'],
            foreground=cls.COLORS['text_white'],
            borderwidth=0,
            focuscolor='none',
            padding=(cls.SPACING['lg'], cls.SPACING['md']),
            font=cls.FONTS['default'],
            width=14
        )
        style.map(
            'DangerAction.TButton',
            background=[('active', cls.COLORS['error_hover']),
                       ('pressed', cls.COLORS['error_hover'])],
            foreground=[('active', cls.COLORS['text_white']),
                       ('pressed', cls.COLORS['text_white'])]
        )

        # ConnectAction button - for connection actions
        style.configure(
            'ConnectAction.TButton',
            background=cls.COLORS['primary'],
            foreground=cls.COLORS['text_white'],
            borderwidth=0,
            focuscolor='none',
            padding=(cls.SPACING['lg'], cls.SPACING['md']),
            font=cls.FONTS['default'],
            width=12
        )
        style.map(
            'ConnectAction.TButton',
            background=[('active', cls.COLORS['primary_hover']),
                       ('pressed', cls.COLORS['primary_hover'])],
            foreground=[('active', cls.COLORS['text_white']),
                       ('pressed', cls.COLORS['text_white'])]
        )

        # PrimaryAction button - for main actions
        style.configure(
            'PrimaryAction.TButton',
            background=cls.COLORS['primary'],
            foreground=cls.COLORS['text_white'],
            borderwidth=0,
            focuscolor='none',
            padding=(cls.SPACING['lg'], cls.SPACING['md']),
            font=cls.FONTS['default'],
            width=14
        )
        style.map(
            'PrimaryAction.TButton',
            background=[('active', cls.COLORS['primary_hover']),
                       ('pressed', cls.COLORS['primary_hover'])],
            foreground=[('active', cls.COLORS['text_white']),
                       ('pressed', cls.COLORS['text_white'])]
        )

        # Small button style for compact areas
        style.configure(
            'Small.TButton',
            background=cls.COLORS['bg_secondary'],
            foreground=cls.COLORS['text_primary'],
            borderwidth=1,
            relief='solid',
            bordercolor=cls.COLORS['border_medium'],
            focuscolor='none',
            padding=(cls.SPACING['md'], cls.SPACING['sm']),
            font=cls.FONTS['small'],
            width=8
        )
        style.map(
            'Small.TButton',
            background=[('active', cls.COLORS['bg_tertiary']),
                       ('pressed', cls.COLORS['bg_tertiary'])],
            foreground=[('active', cls.COLORS['text_primary']),
                       ('pressed', cls.COLORS['text_primary'])]
        )

        # Action button styles with BLACK text (for better visibility)
        # Primary action with black text
        style.configure(
            'PrimaryAction.TButton',
            background=cls.COLORS['primary_light'],
            foreground=cls.COLORS['text_primary'],  # BLACK text
            borderwidth=1,
            relief='solid',
            bordercolor=cls.COLORS['primary'],
            focuscolor='none',
            padding=(cls.SPACING['lg'], cls.SPACING['md']),
            font=cls.FONTS['default'],
            width=14
        )
        style.map(
            'PrimaryAction.TButton',
            background=[('active', cls.COLORS['bg_tertiary']),
                       ('pressed', cls.COLORS['bg_tertiary'])],
            foreground=[('active', cls.COLORS['text_primary']),
                       ('pressed', cls.COLORS['text_primary'])]
        )

        # Success action with black text
        style.configure(
            'SuccessAction.TButton',
            background='#d1fae5',  # Light green background
            foreground=cls.COLORS['text_primary'],  # BLACK text
            borderwidth=1,
            relief='solid',
            bordercolor=cls.COLORS['success'],
            focuscolor='none',
            padding=(cls.SPACING['lg'], cls.SPACING['md']),
            font=cls.FONTS['default'],
            width=12
        )
        style.map(
            'SuccessAction.TButton',
            background=[('active', '#bbf7d0'),  # Slightly darker green on hover
                       ('pressed', '#bbf7d0')],
            foreground=[('active', cls.COLORS['text_primary']),
                       ('pressed', cls.COLORS['text_primary'])]
        )

        # Danger action with black text
        style.configure(
            'DangerAction.TButton',
            background='#fee2e2',  # Light red background
            foreground=cls.COLORS['text_primary'],  # BLACK text
            borderwidth=1,
            relief='solid',
            bordercolor=cls.COLORS['error'],
            focuscolor='none',
            padding=(cls.SPACING['lg'], cls.SPACING['md']),
            font=cls.FONTS['default'],
            width=12
        )
        style.map(
            'DangerAction.TButton',
            background=[('active', '#fecaca'),  # Slightly darker red on hover
                       ('pressed', '#fecaca')],
            foreground=[('active', cls.COLORS['text_primary']),
                       ('pressed', cls.COLORS['text_primary'])]
        )

        # Connect action with black text
        style.configure(
            'ConnectAction.TButton',
            background='#dbeafe',  # Light blue background
            foreground=cls.COLORS['text_primary'],  # BLACK text
            borderwidth=1,
            relief='solid',
            bordercolor=cls.COLORS['info'],
            focuscolor='none',
            padding=(cls.SPACING['lg'], cls.SPACING['md']),
            font=cls.FONTS['default'],
            width=12
        )
        style.map(
            'ConnectAction.TButton',
            background=[('active', '#bfdbfe'),  # Slightly darker blue on hover
                       ('pressed', '#bfdbfe')],
            foreground=[('active', cls.COLORS['text_primary']),
                       ('pressed', cls.COLORS['text_primary'])]
        )

        # Test action with black text
        style.configure(
            'TestAction.TButton',
            background='#f3f4f6',  # Light gray background
            foreground=cls.COLORS['text_primary'],  # BLACK text
            borderwidth=1,
            relief='solid',
            bordercolor=cls.COLORS['border_medium'],
            focuscolor='none',
            padding=(cls.SPACING['lg'], cls.SPACING['md']),
            font=cls.FONTS['default'],
            width=12
        )
        style.map(
            'TestAction.TButton',
            background=[('active', '#e5e7eb'),  # Slightly darker gray on hover
                       ('pressed', '#e5e7eb')],
            foreground=[('active', cls.COLORS['text_primary']),
                       ('pressed', cls.COLORS['text_primary'])]
        )
    
    @classmethod
    def _configure_frame_styles(cls, style: ttk.Style):
        """Configure frame styles with section-specific backgrounds."""
        # Default frame style
        style.configure(
            'TFrame',
            background=cls.COLORS['bg_primary']
        )

        # Card frame with subtle border and background
        style.configure(
            'Card.TFrame',
            background=cls.COLORS['bg_card'],
            relief='solid',
            borderwidth=1,
            bordercolor=cls.COLORS['border_light']
        )

        # Sidebar frame
        style.configure(
            'Sidebar.TFrame',
            background=cls.COLORS['bg_sidebar']
        )

        # Section-specific frame styles for visual separation
        style.configure(
            'Header.TFrame',
            background=cls.COLORS['bg_header'],
            relief='flat',
            borderwidth=0
        )

        style.configure(
            'Transfer.TFrame',
            background=cls.COLORS['bg_transfer'],
            relief='flat',
            borderwidth=0
        )

        style.configure(
            'Server.TFrame',
            background=cls.COLORS['bg_server'],
            relief='flat',
            borderwidth=0
        )

        style.configure(
            'Settings.TFrame',
            background=cls.COLORS['bg_settings'],
            relief='flat',
            borderwidth=0
        )

        style.configure(
            'Status.TFrame',
            background=cls.COLORS['bg_status'],
            relief='flat',
            borderwidth=0
        )

        # Content sections with subtle backgrounds
        style.configure(
            'ContentSection.TFrame',
            background=cls.COLORS['bg_secondary'],
            relief='solid',
            borderwidth=1,
            bordercolor=cls.COLORS['border_light']
        )

        style.configure(
            'ControlPanel.TFrame',
            background=cls.COLORS['bg_tertiary'],
            relief='solid',
            borderwidth=1,
            bordercolor=cls.COLORS['border_medium']
        )

        # LabelFrame styles with enhanced backgrounds
        style.configure(
            'TLabelframe',
            background=cls.COLORS['bg_primary'],
            foreground=cls.COLORS['text_primary'],
            borderwidth=1,
            relief='solid',
            bordercolor=cls.COLORS['border_light']
        )

        style.configure(
            'TLabelframe.Label',
            background=cls.COLORS['bg_primary'],
            foreground=cls.COLORS['text_primary'],
            font=cls.FONTS['subheading']
        )

        # Enhanced LabelFrame for important sections
        style.configure(
            'Important.TLabelframe',
            background=cls.COLORS['bg_card'],
            foreground=cls.COLORS['text_primary'],
            borderwidth=2,
            relief='solid',
            bordercolor=cls.COLORS['border_accent']
        )

        style.configure(
            'Important.TLabelframe.Label',
            background=cls.COLORS['bg_card'],
            foreground=cls.COLORS['text_accent'],
            font=cls.FONTS['subheading']
        )
    
    @classmethod
    def _configure_label_styles(cls, style: ttk.Style):
        """Configure label styles."""
        # Default label style
        style.configure(
            'TLabel',
            background=cls.COLORS['bg_primary'],
            foreground=cls.COLORS['text_primary'],
            font=cls.FONTS['default']
        )

        style.configure(
            'Heading.TLabel',
            background=cls.COLORS['bg_primary'],
            foreground=cls.COLORS['text_primary'],
            font=cls.FONTS['heading']
        )

        style.configure(
            'Subheading.TLabel',
            background=cls.COLORS['bg_primary'],
            foreground=cls.COLORS['text_primary'],
            font=cls.FONTS['subheading']
        )

        style.configure(
            'Muted.TLabel',
            background=cls.COLORS['bg_primary'],
            foreground=cls.COLORS['text_muted'],
            font=cls.FONTS['small']
        )

        style.configure(
            'Success.TLabel',
            background=cls.COLORS['bg_primary'],
            foreground=cls.COLORS['success'],
            font=cls.FONTS['default']
        )

        style.configure(
            'Error.TLabel',
            background=cls.COLORS['bg_primary'],
            foreground=cls.COLORS['error'],
            font=cls.FONTS['default']
        )

        # Section-specific label styles
        style.configure(
            'Header.TLabel',
            background=cls.COLORS['bg_header'],
            foreground=cls.COLORS['text_primary'],
            font=cls.FONTS['default']
        )

        style.configure(
            'Transfer.TLabel',
            background=cls.COLORS['bg_transfer'],
            foreground=cls.COLORS['text_primary'],
            font=cls.FONTS['default']
        )

        style.configure(
            'Server.TLabel',
            background=cls.COLORS['bg_server'],
            foreground=cls.COLORS['text_primary'],
            font=cls.FONTS['default']
        )

        style.configure(
            'Settings.TLabel',
            background=cls.COLORS['bg_settings'],
            foreground=cls.COLORS['text_primary'],
            font=cls.FONTS['default']
        )

        # Sidebar labels with proper contrast
        style.configure(
            'Sidebar.TLabel',
            background=cls.COLORS['bg_sidebar'],
            foreground=cls.COLORS['text_primary'],
            font=cls.FONTS['default']
        )

        style.configure(
            'SidebarMuted.TLabel',
            background=cls.COLORS['bg_sidebar'],
            foreground=cls.COLORS['text_secondary'],
            font=cls.FONTS['small']
        )

        # Status labels with better visibility
        style.configure(
            'Status.TLabel',
            background=cls.COLORS['bg_status'],
            foreground=cls.COLORS['text_secondary'],
            font=cls.FONTS['small']
        )

        # Info labels for better readability
        style.configure(
            'Info.TLabel',
            background=cls.COLORS['bg_primary'],
            foreground=cls.COLORS['text_secondary'],
            font=cls.FONTS['default']
        )

        # Title labels for section headers
        style.configure(
            'Title.TLabel',
            background=cls.COLORS['bg_header'],
            foreground=cls.COLORS['text_primary'],
            font=('Segoe UI', 20, 'bold')
        )

        style.configure(
            'Subtitle.TLabel',
            background=cls.COLORS['bg_header'],
            foreground=cls.COLORS['text_secondary'],
            font=('Segoe UI', 10)
        )
    
    @classmethod
    def _configure_entry_styles(cls, style: ttk.Style):
        """Configure entry styles."""
        # Default entry style
        style.configure(
            'TEntry',
            fieldbackground=cls.COLORS['bg_primary'],
            foreground=cls.COLORS['text_primary'],
            borderwidth=1,
            relief='solid',
            bordercolor=cls.COLORS['border_medium'],
            insertcolor=cls.COLORS['text_primary'],
            font=cls.FONTS['default']
        )
        style.map(
            'TEntry',
            bordercolor=[('focus', cls.COLORS['primary'])],
            fieldbackground=[('focus', cls.COLORS['bg_primary'])]
        )

        style.configure(
            'Modern.TEntry',
            fieldbackground=cls.COLORS['bg_primary'],
            foreground=cls.COLORS['text_primary'],
            borderwidth=1,
            relief='solid',
            bordercolor=cls.COLORS['border_medium'],
            padding=(cls.SPACING['sm'], cls.SPACING['sm']),
            insertcolor=cls.COLORS['text_primary'],
            font=cls.FONTS['default']
        )
        style.map(
            'Modern.TEntry',
            bordercolor=[('focus', cls.COLORS['primary'])],
            fieldbackground=[('focus', cls.COLORS['bg_primary'])]
        )

        # Combobox styles
        style.configure(
            'TCombobox',
            fieldbackground=cls.COLORS['bg_primary'],
            foreground=cls.COLORS['text_primary'],
            background=cls.COLORS['bg_primary'],
            borderwidth=1,
            relief='solid',
            bordercolor=cls.COLORS['border_medium'],
            font=cls.FONTS['default']
        )
        style.map(
            'TCombobox',
            bordercolor=[('focus', cls.COLORS['primary'])],
            fieldbackground=[('focus', cls.COLORS['bg_primary'])]
        )

        style.configure(
            'Modern.TCombobox',
            fieldbackground=cls.COLORS['bg_primary'],
            foreground=cls.COLORS['text_primary'],
            background=cls.COLORS['bg_primary'],
            borderwidth=1,
            relief='solid',
            bordercolor=cls.COLORS['border_medium'],
            font=cls.FONTS['default']
        )
        style.map(
            'Modern.TCombobox',
            bordercolor=[('focus', cls.COLORS['primary'])],
            fieldbackground=[('focus', cls.COLORS['bg_primary'])]
        )
    
    @classmethod
    def _configure_notebook_styles(cls, style: ttk.Style):
        """Configure notebook (tab) styles."""
        style.configure(
            'Modern.TNotebook',
            background=cls.COLORS['bg_primary'],
            borderwidth=0
        )
        
        style.configure(
            'Modern.TNotebook.Tab',
            background=cls.COLORS['bg_secondary'],
            foreground=cls.COLORS['text_secondary'],
            padding=(cls.SPACING['lg'], cls.SPACING['sm']),
            font=cls.FONTS['default']
        )
        
        style.map(
            'Modern.TNotebook.Tab',
            background=[('selected', cls.COLORS['bg_primary']),
                       ('active', cls.COLORS['bg_tertiary'])],
            foreground=[('selected', cls.COLORS['text_primary'])]
        )
    
    @classmethod
    def _configure_progressbar_styles(cls, style: ttk.Style):
        """Configure progress bar styles."""
        style.configure(
            'Modern.Horizontal.TProgressbar',
            background=cls.COLORS['primary'],
            troughcolor=cls.COLORS['bg_tertiary'],
            borderwidth=0,
            lightcolor=cls.COLORS['primary'],
            darkcolor=cls.COLORS['primary']
        )
    
    @classmethod
    def _configure_treeview_styles(cls, style: ttk.Style):
        """Configure treeview styles."""
        style.configure(
            'Modern.Treeview',
            background=cls.COLORS['bg_primary'],
            foreground=cls.COLORS['text_primary'],
            fieldbackground=cls.COLORS['bg_primary'],
            borderwidth=1,
            relief='solid',
            font=cls.FONTS['default']
        )
        
        style.configure(
            'Modern.Treeview.Heading',
            background=cls.COLORS['bg_secondary'],
            foreground=cls.COLORS['text_primary'],
            font=cls.FONTS['subheading']
        )


class IconManager:
    """
    Manager for modern icons using Unicode symbols.
    """
    
    ICONS = {
        # File operations
        'file': '📄',
        'folder': '📁',
        'upload': '📤',
        'download': '📥',
        'transfer': '🔄',
        
        # Network
        'connect': '🔗',
        'disconnect': '🔌',
        'network': '🌐',
        'server': '🖥️',
        'client': '📱',
        
        # Status
        'success': '✅',
        'error': '❌',
        'warning': '⚠️',
        'info': 'ℹ️',
        'progress': '⏳',
        
        # Actions
        'play': '▶️',
        'pause': '⏸️',
        'stop': '⏹️',
        'settings': '⚙️',
        'refresh': '🔄',
        'clear': '🗑️',
        'add': '➕',
        'remove': '➖',
        
        # Security
        'lock': '🔒',
        'unlock': '🔓',
        'key': '🔑',
        'shield': '🛡️',
    }
    
    @classmethod
    def get_icon(cls, name: str) -> str:
        """Get an icon by name."""
        return cls.ICONS.get(name, '•')
    
    @classmethod
    def get_status_icon(cls, status: str) -> str:
        """Get a status icon."""
        status_map = {
            'connected': cls.ICONS['success'],
            'disconnected': cls.ICONS['error'],
            'connecting': cls.ICONS['progress'],
            'transferring': cls.ICONS['transfer'],
            'completed': cls.ICONS['success'],
            'failed': cls.ICONS['error'],
            'ready': cls.ICONS['info'],
        }
        return status_map.get(status.lower(), cls.ICONS['info'])

"""
Network utilities for the file transfer application.
"""

import socket
import ipaddress
import platform
import subprocess
from typing import List, Optional, Dict, Any
from src.utils.logger import get_logger


class NetworkUtils:
    """
    Utility class for network operations.
    """
    
    @staticmethod
    def get_local_ip() -> str:
        """
        Get the local IP address.
        
        Returns:
            Local IP address as string
        """
        try:
            # Connect to a remote address to determine local IP
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))
                local_ip = s.getsockname()[0]
            return local_ip
        except Exception:
            return "127.0.0.1"
    
    @staticmethod
    def get_all_local_ips() -> List[str]:
        """
        Get all local IP addresses.
        
        Returns:
            List of local IP addresses
        """
        ips = []
        
        try:
            hostname = socket.gethostname()
            # Get all addresses for the hostname
            for info in socket.getaddrinfo(hostname, None):
                ip = info[4][0]
                if ip not in ips and not ip.startswith("127."):
                    ips.append(ip)
        except Exception:
            pass
        
        # Add localhost
        if "127.0.0.1" not in ips:
            ips.append("127.0.0.1")
        
        return ips
    
    @staticmethod
    def is_port_available(host: str, port: int) -> bool:
        """
        Check if a port is available on the given host.
        
        Args:
            host: Host address
            port: Port number
            
        Returns:
            True if port is available, False otherwise
        """
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(1)
                result = s.connect_ex((host, port))
                return result != 0  # Port is available if connection fails
        except Exception:
            return False
    
    @staticmethod
    def find_available_port(host: str = "localhost", start_port: int = 8888, max_attempts: int = 100) -> Optional[int]:
        """
        Find an available port starting from the given port.
        
        Args:
            host: Host address
            start_port: Starting port number
            max_attempts: Maximum number of ports to try
            
        Returns:
            Available port number or None if no port found
        """
        for port in range(start_port, start_port + max_attempts):
            if NetworkUtils.is_port_available(host, port):
                return port
        return None
    
    @staticmethod
    def is_valid_ip(ip: str) -> bool:
        """
        Check if the given string is a valid IP address.
        
        Args:
            ip: IP address string
            
        Returns:
            True if valid IP address, False otherwise
        """
        try:
            ipaddress.ip_address(ip)
            return True
        except ValueError:
            return False
    
    @staticmethod
    def is_valid_port(port: int) -> bool:
        """
        Check if the given port number is valid.
        
        Args:
            port: Port number
            
        Returns:
            True if valid port, False otherwise
        """
        return 1 <= port <= 65535
    
    @staticmethod
    def test_connection(host: str, port: int, timeout: int = 5) -> bool:
        """
        Test if a connection can be established to the given host and port.
        
        Args:
            host: Host address
            port: Port number
            timeout: Connection timeout in seconds
            
        Returns:
            True if connection successful, False otherwise
        """
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(timeout)
                result = s.connect_ex((host, port))
                return result == 0
        except Exception:
            return False
    
    @staticmethod
    def get_network_interfaces() -> List[Dict[str, Any]]:
        """
        Get information about network interfaces.
        
        Returns:
            List of network interface information
        """
        interfaces = []
        
        try:
            if platform.system() == "Windows":
                # Use ipconfig on Windows
                result = subprocess.run(
                    ["ipconfig", "/all"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                # Parse ipconfig output (simplified)
                lines = result.stdout.split('\n')
                current_interface = None
                
                for line in lines:
                    line = line.strip()
                    if "adapter" in line.lower() and ":" in line:
                        current_interface = {
                            "name": line.split(":")[0].strip(),
                            "ips": []
                        }
                        interfaces.append(current_interface)
                    elif current_interface and "IPv4 Address" in line:
                        ip = line.split(":")[-1].strip()
                        if NetworkUtils.is_valid_ip(ip):
                            current_interface["ips"].append(ip)
            
            else:
                # Use ifconfig on Unix-like systems
                result = subprocess.run(
                    ["ifconfig"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                # Parse ifconfig output (simplified)
                lines = result.stdout.split('\n')
                current_interface = None
                
                for line in lines:
                    if line and not line.startswith(' ') and not line.startswith('\t'):
                        # New interface
                        interface_name = line.split(':')[0].strip()
                        current_interface = {
                            "name": interface_name,
                            "ips": []
                        }
                        interfaces.append(current_interface)
                    elif current_interface and "inet " in line:
                        # Extract IP address
                        parts = line.split()
                        for i, part in enumerate(parts):
                            if part == "inet" and i + 1 < len(parts):
                                ip = parts[i + 1]
                                if NetworkUtils.is_valid_ip(ip):
                                    current_interface["ips"].append(ip)
                                break
        
        except Exception as e:
            get_logger().error(f"Error getting network interfaces: {e}")
        
        return interfaces
    
    @staticmethod
    def get_hostname() -> str:
        """
        Get the system hostname.
        
        Returns:
            System hostname
        """
        try:
            return socket.gethostname()
        except Exception:
            return "unknown"
    
    @staticmethod
    def resolve_hostname(hostname: str) -> Optional[str]:
        """
        Resolve hostname to IP address.
        
        Args:
            hostname: Hostname to resolve
            
        Returns:
            IP address or None if resolution failed
        """
        try:
            return socket.gethostbyname(hostname)
        except Exception:
            return None
    
    @staticmethod
    def get_connection_info(socket_obj: socket.socket) -> Dict[str, Any]:
        """
        Get connection information from a socket.
        
        Args:
            socket_obj: Socket object
            
        Returns:
            Dictionary with connection information
        """
        try:
            local_addr = socket_obj.getsockname()
            peer_addr = socket_obj.getpeername()
            
            return {
                "local_host": local_addr[0],
                "local_port": local_addr[1],
                "peer_host": peer_addr[0],
                "peer_port": peer_addr[1],
                "family": socket_obj.family.name,
                "type": socket_obj.type.name
            }
        except Exception as e:
            get_logger().error(f"Error getting connection info: {e}")
            return {}
    
    @staticmethod
    def format_address(host: str, port: int) -> str:
        """
        Format host and port as a readable address string.
        
        Args:
            host: Host address
            port: Port number
            
        Returns:
            Formatted address string
        """
        if NetworkUtils.is_valid_ip(host):
            if ":" in host:  # IPv6
                return f"[{host}]:{port}"
            else:  # IPv4
                return f"{host}:{port}"
        else:  # Hostname
            return f"{host}:{port}"

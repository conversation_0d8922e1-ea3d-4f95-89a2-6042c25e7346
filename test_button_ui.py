#!/usr/bin/env python3
"""
Test script to verify button sizes and text colors in the File Transfer application.
"""

import tkinter as tk
from tkinter import ttk
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.gui.theme import ModernTheme
from src.gui.settings_dialog import SettingsDialog


def test_button_styles():
    """Test all button styles and their properties."""
    print("🔍 Testing Button Styles and Text Colors")
    print("=" * 50)
    
    # Create test window
    root = tk.Tk()
    root.title("Button Style Test")
    root.geometry("800x600")
    
    # Apply modern theme
    style = ModernTheme.apply_theme(root)
    
    # Create main frame
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # Title
    title_label = ttk.Label(main_frame, text="🎨 Button Style & Color Test", style='Heading.TLabel')
    title_label.pack(pady=(0, 20))
    
    # Test different button styles
    button_styles = [
        ("Default Button", "TButton"),
        ("Primary Button", "Primary.TButton"),
        ("Secondary Button", "Secondary.TButton"),
        ("Success Button", "Success.TButton"),
        ("Danger Button", "Danger.TButton"),
        ("Small Button", "Small.TButton")
    ]
    
    print("\n📏 Button Style Properties:")
    print("-" * 30)
    
    for i, (label, style_name) in enumerate(button_styles):
        # Create button
        btn = ttk.Button(main_frame, text=label, style=style_name)
        btn.pack(pady=5, padx=20, fill=tk.X)
        
        # Get style configuration
        config = style.configure(style_name)
        if config:
            print(f"\n{label} ({style_name}):")
            for key, value in config.items():
                print(f"  {key}: {value}")
        else:
            print(f"\n{label} ({style_name}): Using default configuration")
        
        # Test button click
        btn.configure(command=lambda l=label: print(f"✅ {l} clicked - Text is visible and readable"))
    
    # Add separator
    separator = ttk.Separator(main_frame, orient='horizontal')
    separator.pack(fill=tk.X, pady=20)
    
    # Test settings dialog button
    settings_btn = ttk.Button(
        main_frame, 
        text="🔧 Open Enhanced Settings Dialog", 
        style='Primary.TButton',
        command=lambda: test_settings_dialog(root)
    )
    settings_btn.pack(pady=10)
    
    # Color information
    info_frame = ttk.LabelFrame(main_frame, text="Color Information", padding="15")
    info_frame.pack(fill=tk.X, pady=(20, 0))
    
    color_info = f"""
🎨 Theme Colors:
• Primary: {ModernTheme.COLORS['primary']} (White text)
• Secondary: {ModernTheme.COLORS['bg_secondary']} (Dark text)
• Success: {ModernTheme.COLORS['success']} (White text)
• Error: {ModernTheme.COLORS['error']} (White text)
• Text Primary: {ModernTheme.COLORS['text_primary']}
• Text Secondary: {ModernTheme.COLORS['text_secondary']}
• Text Muted: {ModernTheme.COLORS['text_muted']}

📐 Button Sizing:
• Default padding: {ModernTheme.SPACING['lg']}px x {ModernTheme.SPACING['md']}px
• Small padding: {ModernTheme.SPACING['md']}px x {ModernTheme.SPACING['sm']}px
• Minimum width: 12 characters (14 for primary)
"""
    
    info_label = ttk.Label(info_frame, text=color_info, justify=tk.LEFT, style='Muted.TLabel')
    info_label.pack(anchor=tk.W)
    
    # Instructions
    instructions = ttk.Label(
        main_frame,
        text="👆 Click each button to test visibility and functionality",
        style='Muted.TLabel'
    )
    instructions.pack(pady=(10, 0))
    
    print(f"\n🎨 Color Contrast Information:")
    print(f"Primary button: {ModernTheme.COLORS['primary']} background, white text")
    print(f"Secondary button: {ModernTheme.COLORS['bg_secondary']} background, {ModernTheme.COLORS['text_primary']} text")
    print(f"Success button: {ModernTheme.COLORS['success']} background, white text")
    print(f"Danger button: {ModernTheme.COLORS['error']} background, white text")
    
    print(f"\n📐 Button Sizing:")
    print(f"Standard padding: {ModernTheme.SPACING['lg']}px horizontal, {ModernTheme.SPACING['md']}px vertical")
    print(f"Small padding: {ModernTheme.SPACING['md']}px horizontal, {ModernTheme.SPACING['sm']}px vertical")
    print(f"Minimum width: 12 characters (14 for primary buttons)")
    
    print(f"\n✅ All buttons should have:")
    print("• Consistent sizing and padding")
    print("• High contrast text that's easy to read")
    print("• Proper hover effects")
    print("• Clear visual hierarchy")
    
    root.mainloop()


def test_settings_dialog(parent):
    """Test the enhanced settings dialog."""
    print("\n🔧 Testing Enhanced Settings Dialog...")
    
    try:
        dialog = SettingsDialog(parent)
        result = dialog.show()
        
        if result:
            print("✅ Settings dialog completed successfully")
            print("📋 Settings returned:")
            for key, value in result.items():
                print(f"  {key}: {value}")
        else:
            print("❌ Settings dialog was cancelled")
            
    except Exception as e:
        print(f"❌ Settings dialog error: {e}")
        import traceback
        traceback.print_exc()


def test_color_contrast():
    """Test color contrast ratios."""
    print("\n🔍 Testing Color Contrast Ratios:")
    print("-" * 35)
    
    def hex_to_rgb(hex_color):
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    def calculate_luminance(rgb):
        r, g, b = [x / 255.0 for x in rgb]
        
        def gamma_correct(c):
            return c / 12.92 if c <= 0.03928 else ((c + 0.055) / 1.055) ** 2.4
        
        r, g, b = map(gamma_correct, [r, g, b])
        return 0.2126 * r + 0.7152 * g + 0.0722 * b
    
    def contrast_ratio(color1, color2):
        lum1 = calculate_luminance(hex_to_rgb(color1))
        lum2 = calculate_luminance(hex_to_rgb(color2))
        
        lighter = max(lum1, lum2)
        darker = min(lum1, lum2)
        
        return (lighter + 0.05) / (darker + 0.05)
    
    # Test key color combinations
    test_combinations = [
        ("Primary button", ModernTheme.COLORS['primary'], ModernTheme.COLORS['text_white']),
        ("Secondary button", ModernTheme.COLORS['bg_secondary'], ModernTheme.COLORS['text_primary']),
        ("Success button", ModernTheme.COLORS['success'], ModernTheme.COLORS['text_white']),
        ("Error button", ModernTheme.COLORS['error'], ModernTheme.COLORS['text_white']),
        ("Primary text", ModernTheme.COLORS['bg_primary'], ModernTheme.COLORS['text_primary']),
        ("Muted text", ModernTheme.COLORS['bg_primary'], ModernTheme.COLORS['text_muted']),
    ]
    
    for name, bg_color, text_color in test_combinations:
        ratio = contrast_ratio(bg_color, text_color)
        status = "✅ PASS" if ratio >= 4.5 else "⚠️ WARN" if ratio >= 3.0 else "❌ FAIL"
        print(f"{name}: {ratio:.2f}:1 {status}")
    
    print(f"\n📋 Contrast Guidelines:")
    print("• 4.5:1 - Minimum for normal text (WCAG AA)")
    print("• 3.0:1 - Minimum for large text")
    print("• 7.0:1 - Enhanced contrast (WCAG AAA)")


if __name__ == "__main__":
    print("🚀 File Transfer Application - Button & UI Test")
    print("=" * 55)
    
    # Test color contrast first
    test_color_contrast()
    
    # Test button styles
    test_button_styles()
    
    print("\n" + "=" * 55)
    print("✅ Button and UI testing completed!")
    print("📋 Check the GUI window to verify visual appearance")

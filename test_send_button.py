#!/usr/bin/env python3
"""
Test script to verify send button functionality.
"""

import time
import threading
from pathlib import Path
from src.core.resilient_server import ResilientFileTransferServer

def create_test_file(filename: str, size_kb: int = 50):
    """Create a test file of specified size."""
    test_file = Path(filename)
    with open(test_file, 'w') as f:
        # Write some test content
        content = "This is a test file for send button verification.\n" * (size_kb * 5)  # Approximate KB
        f.write(content)
    return test_file

def start_test_server():
    """Start a test server for the GUI to connect to."""
    print("Starting test server on localhost:8888...")
    
    # Setup server
    download_dir = Path("test_gui_downloads")
    download_dir.mkdir(exist_ok=True)
    
    server = ResilientFileTransferServer(
        host="localhost",
        port=8888,
        download_dir=str(download_dir)
    )
    
    # Setup server callbacks
    def on_file_received(filename, client_id):
        print(f"✓ Server: File received - {filename} from {client_id}")
    
    server.on_file_received = on_file_received
    
    # Start server
    try:
        server.start()
    except KeyboardInterrupt:
        print("\nStopping server...")
        server.stop()
        
        # Cleanup
        for file in download_dir.glob("*"):
            file.unlink()
        download_dir.rmdir()

if __name__ == "__main__":
    print("=== Send Button Test Setup ===")
    print("1. Creating test file...")
    
    # Create test file
    test_file = create_test_file("send_test.txt", 20)  # 20KB file
    print(f"   Created: {test_file} ({test_file.stat().st_size} bytes)")
    
    print("\n2. Starting test server...")
    print("   Server will run on localhost:8888")
    print("   Download directory: test_gui_downloads/")
    
    print("\n3. Instructions for testing:")
    print("   a. Run the GUI application (python main.py)")
    print("   b. Make sure 'Resilient Transfers' is enabled")
    print("   c. Connect to localhost:8888")
    print("   d. Add the test file 'send_test.txt' to the file list")
    print("   e. Click 'Send Files' button")
    print("   f. Verify the file transfer works and control buttons appear")
    
    print("\n4. Press Ctrl+C to stop the server when done testing")
    print("\nStarting server now...\n")
    
    try:
        start_test_server()
    finally:
        # Cleanup test file
        if test_file.exists():
            test_file.unlink()
            print(f"Cleaned up test file: {test_file}")

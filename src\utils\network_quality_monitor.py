"""
Network quality monitoring with latency, jitter, and packet loss detection.
"""

import time
import socket
import threading
import statistics
from typing import Dict, List, Optional, Callable, Tuple
from dataclasses import dataclass
from enum import Enum
from src.utils.logger import get_logger


class QualityLevel(Enum):
    """Network quality levels."""
    EXCELLENT = "excellent"
    GOOD = "good"
    FAIR = "fair"
    POOR = "poor"
    VERY_POOR = "very_poor"


@dataclass
class QualityMetrics:
    """Network quality metrics."""
    latency_ms: float
    jitter_ms: float
    packet_loss_percent: float
    quality_level: QualityLevel
    timestamp: float


class NetworkQualityMonitor:
    """
    Monitor network quality including latency, jitter, and packet loss.
    """
    
    def __init__(self, test_hosts: Optional[List[Tuple[str, int]]] = None):
        """
        Initialize the network quality monitor.
        
        Args:
            test_hosts: List of (host, port) tuples for quality testing
        """
        self.logger = get_logger()
        
        # Test hosts for quality monitoring
        self.test_hosts = test_hosts or [
            ("*******", 53),  # Google DNS
            ("*******", 53),  # Cloudflare DNS
            ("**************", 53),  # OpenDNS
        ]
        
        # Monitoring state
        self.monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.monitor_interval = 10.0  # seconds
        
        # Quality history
        self.quality_history: List[QualityMetrics] = []
        self.max_history = 100
        
        # Current metrics
        self.current_metrics = QualityMetrics(
            latency_ms=0.0,
            jitter_ms=0.0,
            packet_loss_percent=0.0,
            quality_level=QualityLevel.FAIR,
            timestamp=time.time()
        )
        
        # Callbacks
        self.on_quality_updated: Optional[Callable[[QualityMetrics], None]] = None
        self.on_quality_degraded: Optional[Callable[[QualityLevel], None]] = None
        self.on_quality_improved: Optional[Callable[[QualityLevel], None]] = None
        
        # Configuration
        self.test_samples = 5
        self.test_timeout = 2.0
        
    def start_monitoring(self):
        """Start continuous quality monitoring."""
        if self.monitoring:
            return
            
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self.monitor_thread.start()
        self.logger.info("Network quality monitoring started")
        
    def stop_monitoring(self):
        """Stop quality monitoring."""
        self.monitoring = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=2.0)
        self.logger.info("Network quality monitoring stopped")
        
    def measure_quality(self) -> QualityMetrics:
        """
        Measure current network quality.
        
        Returns:
            Current quality metrics
        """
        latencies = []
        successful_tests = 0
        total_tests = 0
        
        for host, port in self.test_hosts:
            for _ in range(self.test_samples):
                total_tests += 1
                latency = self._measure_latency(host, port)
                
                if latency > 0:
                    latencies.append(latency)
                    successful_tests += 1
                    
                time.sleep(0.1)  # Small delay between tests
                
        # Calculate metrics
        if latencies:
            avg_latency = statistics.mean(latencies)
            jitter = statistics.stdev(latencies) if len(latencies) > 1 else 0.0
        else:
            avg_latency = 0.0
            jitter = 0.0
            
        # Calculate packet loss
        packet_loss = ((total_tests - successful_tests) / total_tests) * 100 if total_tests > 0 else 100.0
        
        # Determine quality level
        quality_level = self._assess_quality(avg_latency, jitter, packet_loss)
        
        metrics = QualityMetrics(
            latency_ms=avg_latency,
            jitter_ms=jitter,
            packet_loss_percent=packet_loss,
            quality_level=quality_level,
            timestamp=time.time()
        )
        
        return metrics
        
    def get_current_metrics(self) -> QualityMetrics:
        """Get current quality metrics."""
        return self.current_metrics
        
    def get_quality_trend(self, minutes: int = 5) -> List[QualityMetrics]:
        """
        Get quality trend for the specified time period.
        
        Args:
            minutes: Number of minutes to look back
            
        Returns:
            List of quality metrics within the time period
        """
        cutoff_time = time.time() - (minutes * 60)
        return [m for m in self.quality_history if m.timestamp >= cutoff_time]
        
    def is_quality_degraded(self) -> bool:
        """Check if network quality is currently degraded."""
        return self.current_metrics.quality_level in [QualityLevel.POOR, QualityLevel.VERY_POOR]
        
    def get_quality_score(self) -> float:
        """
        Get a numeric quality score (0-100).
        
        Returns:
            Quality score where 100 is excellent and 0 is very poor
        """
        if self.current_metrics.quality_level == QualityLevel.EXCELLENT:
            return 90 + min(10, (100 - self.current_metrics.latency_ms) / 10)
        elif self.current_metrics.quality_level == QualityLevel.GOOD:
            return 70 + min(20, (200 - self.current_metrics.latency_ms) / 10)
        elif self.current_metrics.quality_level == QualityLevel.FAIR:
            return 50 + min(20, (300 - self.current_metrics.latency_ms) / 10)
        elif self.current_metrics.quality_level == QualityLevel.POOR:
            return 25 + min(25, (500 - self.current_metrics.latency_ms) / 20)
        else:  # VERY_POOR
            return max(0, 25 - (self.current_metrics.latency_ms - 500) / 50)
            
    def _monitoring_loop(self):
        """Main monitoring loop."""
        while self.monitoring:
            try:
                # Measure quality
                metrics = self.measure_quality()
                
                # Update current metrics
                old_quality = self.current_metrics.quality_level
                self.current_metrics = metrics
                
                # Add to history
                self.quality_history.append(metrics)
                if len(self.quality_history) > self.max_history:
                    self.quality_history.pop(0)
                    
                # Check for quality changes
                if old_quality != metrics.quality_level:
                    if self._is_quality_worse(metrics.quality_level, old_quality):
                        if self.on_quality_degraded:
                            self.on_quality_degraded(metrics.quality_level)
                    else:
                        if self.on_quality_improved:
                            self.on_quality_improved(metrics.quality_level)
                            
                # Notify listeners
                if self.on_quality_updated:
                    self.on_quality_updated(metrics)
                    
                self.logger.debug(f"Quality metrics: latency={metrics.latency_ms:.1f}ms, "
                                f"jitter={metrics.jitter_ms:.1f}ms, "
                                f"loss={metrics.packet_loss_percent:.1f}%, "
                                f"quality={metrics.quality_level.value}")
                
                # Sleep until next measurement
                time.sleep(self.monitor_interval)
                
            except Exception as e:
                self.logger.error(f"Error in quality monitoring loop: {e}")
                time.sleep(1.0)
                
    def _measure_latency(self, host: str, port: int) -> float:
        """
        Measure latency to a specific host.
        
        Args:
            host: Target host
            port: Target port
            
        Returns:
            Latency in milliseconds, 0 if failed
        """
        try:
            start_time = time.time()
            
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(self.test_timeout)
                sock.connect((host, port))
                
            end_time = time.time()
            return (end_time - start_time) * 1000  # Convert to milliseconds
            
        except Exception:
            return 0.0
            
    def _assess_quality(self, latency: float, jitter: float, packet_loss: float) -> QualityLevel:
        """
        Assess overall network quality based on metrics.
        
        Args:
            latency: Average latency in milliseconds
            jitter: Jitter in milliseconds
            packet_loss: Packet loss percentage
            
        Returns:
            Quality level assessment
        """
        # Define quality thresholds
        if latency < 50 and jitter < 10 and packet_loss < 1:
            return QualityLevel.EXCELLENT
        elif latency < 100 and jitter < 20 and packet_loss < 3:
            return QualityLevel.GOOD
        elif latency < 200 and jitter < 50 and packet_loss < 5:
            return QualityLevel.FAIR
        elif latency < 500 and jitter < 100 and packet_loss < 10:
            return QualityLevel.POOR
        else:
            return QualityLevel.VERY_POOR
            
    def _is_quality_worse(self, new_quality: QualityLevel, old_quality: QualityLevel) -> bool:
        """Check if new quality is worse than old quality."""
        quality_order = [
            QualityLevel.EXCELLENT,
            QualityLevel.GOOD,
            QualityLevel.FAIR,
            QualityLevel.POOR,
            QualityLevel.VERY_POOR
        ]
        
        try:
            return quality_order.index(new_quality) > quality_order.index(old_quality)
        except ValueError:
            return False

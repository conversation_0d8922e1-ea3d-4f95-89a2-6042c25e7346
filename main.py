#!/usr/bin/env python3
"""
File Transfer Application - Main Entry Point

Usage:
    python main.py                    # Start GUI mode
    python main.py --mode server      # Start as server (receiver)
    python main.py --mode client      # Start as client (sender)
    python main.py --help            # Show help
"""

import sys
import argparse
import logging
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.logger import setup_logger
from src.core.server import FileTransferServer
from src.core.client import FileTransferClient
from src.gui.main_window import MainWindow


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="File Transfer Application - Send large files securely over network",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                     # Start GUI mode
  python main.py --mode server       # Start as server on default port
  python main.py --mode client       # Start as client
  python main.py --mode server --port 8080 --host 0.0.0.0
        """
    )
    
    parser.add_argument(
        '--mode', 
        choices=['gui', 'server', 'client'],
        default='gui',
        help='Application mode (default: gui)'
    )
    
    parser.add_argument(
        '--host',
        default='localhost',
        help='Host address (default: localhost)'
    )
    
    parser.add_argument(
        '--port',
        type=int,
        default=8888,
        help='Port number (default: 8888)'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )
    
    parser.add_argument(
        '--log-file',
        help='Log file path (default: console only)'
    )

    parser.add_argument(
        '--classic-ui',
        action='store_true',
        help='Use classic UI instead of modern UI'
    )
    
    return parser.parse_args()


def main():
    """Main application entry point."""
    args = parse_arguments()
    
    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logger = setup_logger(level=log_level, log_file=args.log_file)
    
    logger.info(f"Starting File Transfer Application in {args.mode} mode")
    logger.info(f"Host: {args.host}, Port: {args.port}")
    
    try:
        if args.mode == 'gui':
            # Start GUI mode with improved UI
            logger.info("Starting GUI mode with enhanced modern design")
            app = MainWindow()
            app.run()
            
        elif args.mode == 'server':
            # Start server mode
            logger.info(f"Starting server on {args.host}:{args.port}")
            server = FileTransferServer(host=args.host, port=args.port)
            server.start()
            
        elif args.mode == 'client':
            # Start client mode
            logger.info(f"Starting client, connecting to {args.host}:{args.port}")
            client = FileTransferClient()
            # In CLI mode, we'll need to implement interactive file selection
            print("Client mode - GUI recommended for file selection")
            print("Use: python main.py --mode gui")
            
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
        print("\nApplication stopped by user")
    except Exception as e:
        logger.error(f"Application error: {e}", exc_info=True)
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

"""
Adaptive transfer speed controller for dynamic bandwidth optimization.
"""

import time
import threading
from typing import Dict, Optional, Callable, Tuple
from dataclasses import dataclass
from enum import Enum
from src.utils.logger import get_logger
from src.utils.bandwidth_detector import BandwidthDetector, NetworkMetrics, NetworkQuality
from src.utils.network_quality_monitor import NetworkQualityMonitor, QualityMetrics, QualityLevel


class SpeedMode(Enum):
    """Transfer speed modes."""
    CONSERVATIVE = "conservative"
    BALANCED = "balanced"
    AGGRESSIVE = "aggressive"
    CUSTOM = "custom"


@dataclass
class SpeedSettings:
    """Speed control settings."""
    max_speed_bps: Optional[float] = None  # None = unlimited
    mode: SpeedMode = SpeedMode.BALANCED
    enable_congestion_control: bool = True
    enable_speed_ramping: bool = True
    chunk_size_min: int = 8 * 1024  # 8KB
    chunk_size_max: int = 1024 * 1024  # 1MB
    buffer_size_min: int = 64 * 1024  # 64KB
    buffer_size_max: int = 4 * 1024 * 1024  # 4MB
    ramp_up_factor: float = 1.2
    ramp_down_factor: float = 0.8
    congestion_threshold: float = 0.8  # 80% utilization


@dataclass
class TransferParameters:
    """Dynamic transfer parameters."""
    chunk_size: int
    buffer_size: int
    target_speed_bps: float
    max_concurrent_chunks: int
    socket_timeout: float


class AdaptiveSpeedController:
    """
    Adaptive speed controller that dynamically optimizes transfer parameters
    based on network conditions and performance metrics.
    """
    
    def __init__(self, bandwidth_detector: Optional[BandwidthDetector] = None):
        """
        Initialize the speed controller.

        Args:
            bandwidth_detector: Bandwidth detector instance
        """
        self.logger = get_logger()
        self.bandwidth_detector = bandwidth_detector or BandwidthDetector()
        self.quality_monitor = NetworkQualityMonitor()
        
        # Settings
        self.settings = SpeedSettings()
        
        # Current state
        self.current_params = TransferParameters(
            chunk_size=64 * 1024,  # 64KB
            buffer_size=256 * 1024,  # 256KB
            target_speed_bps=0.0,
            max_concurrent_chunks=1,
            socket_timeout=10.0
        )
        
        # Performance tracking
        self.recent_speeds: list = []
        self.max_speed_history = 20
        self.last_adjustment_time = 0.0
        self.adjustment_interval = 2.0  # seconds
        
        # State tracking
        self.ramping_up = False
        self.ramping_down = False
        self.congestion_detected = False
        
        # Callbacks
        self.on_parameters_updated: Optional[Callable[[TransferParameters], None]] = None
        self.on_speed_limit_reached: Optional[Callable[[float], None]] = None
        
        # Lock for thread safety
        self.lock = threading.Lock()
        
    def configure(self, settings: SpeedSettings):
        """Configure speed control settings."""
        with self.lock:
            self.settings = settings
            self._recalculate_parameters()
            
    def start_optimization(self):
        """Start the speed optimization system."""
        self.bandwidth_detector.on_metrics_updated = self._on_network_metrics_updated
        self.bandwidth_detector.start_monitoring()

        # Start quality monitoring
        self.quality_monitor.on_quality_updated = self._on_quality_updated
        self.quality_monitor.on_quality_degraded = self._on_quality_degraded
        self.quality_monitor.start_monitoring()

        # Initial bandwidth detection
        available_bandwidth = self.bandwidth_detector.detect_available_bandwidth()
        self._initialize_parameters(available_bandwidth)

        self.logger.info("Speed optimization started")
        
    def stop_optimization(self):
        """Stop the speed optimization system."""
        self.bandwidth_detector.stop_monitoring()
        self.quality_monitor.stop_monitoring()
        self.logger.info("Speed optimization stopped")
        
    def update_transfer_performance(self, bytes_transferred: int, duration: float):
        """
        Update controller with recent transfer performance.
        
        Args:
            bytes_transferred: Bytes transferred in the measurement period
            duration: Duration of the measurement period in seconds
        """
        if duration <= 0:
            return
            
        current_speed = bytes_transferred / duration
        
        with self.lock:
            # Add to recent speeds
            self.recent_speeds.append(current_speed)
            if len(self.recent_speeds) > self.max_speed_history:
                self.recent_speeds.pop(0)
            
            # Check if we should adjust parameters
            current_time = time.time()
            if current_time - self.last_adjustment_time >= self.adjustment_interval:
                self._adjust_parameters(current_speed)
                self.last_adjustment_time = current_time
                
    def get_current_parameters(self) -> TransferParameters:
        """Get current transfer parameters."""
        with self.lock:
            return self.current_params
            
    def get_optimal_chunk_size(self) -> int:
        """Get optimal chunk size for current conditions."""
        return self.current_params.chunk_size
        
    def get_optimal_buffer_size(self) -> int:
        """Get optimal buffer size for current conditions."""
        return self.current_params.buffer_size
        
    def get_target_speed(self) -> float:
        """Get target transfer speed in bytes per second."""
        return self.current_params.target_speed_bps
        
    def should_throttle(self, current_speed: float) -> bool:
        """
        Check if transfer should be throttled.
        
        Args:
            current_speed: Current transfer speed in bytes per second
            
        Returns:
            True if transfer should be throttled
        """
        if self.settings.max_speed_bps is None:
            return False
            
        return current_speed > self.settings.max_speed_bps
        
    def calculate_throttle_delay(self, current_speed: float, chunk_size: int) -> float:
        """
        Calculate delay needed to throttle speed.
        
        Args:
            current_speed: Current transfer speed in bytes per second
            chunk_size: Size of the chunk being sent
            
        Returns:
            Delay in seconds
        """
        if not self.should_throttle(current_speed) or self.settings.max_speed_bps is None:
            return 0.0
            
        target_time = chunk_size / self.settings.max_speed_bps
        actual_time = chunk_size / current_speed
        
        return max(0.0, target_time - actual_time)
        
    def _initialize_parameters(self, available_bandwidth: float):
        """Initialize transfer parameters based on available bandwidth."""
        with self.lock:
            # Set target speed based on mode and available bandwidth
            if self.settings.mode == SpeedMode.CONSERVATIVE:
                target_speed = available_bandwidth * 0.5
            elif self.settings.mode == SpeedMode.BALANCED:
                target_speed = available_bandwidth * 0.7
            elif self.settings.mode == SpeedMode.AGGRESSIVE:
                target_speed = available_bandwidth * 0.9
            else:  # CUSTOM
                target_speed = available_bandwidth * 0.7
                
            # Apply user-defined speed limit
            if self.settings.max_speed_bps is not None:
                target_speed = min(target_speed, self.settings.max_speed_bps)
                
            # Calculate optimal chunk size based on target speed
            chunk_size = self._calculate_optimal_chunk_size(target_speed)
            buffer_size = self._calculate_optimal_buffer_size(target_speed)
            
            self.current_params = TransferParameters(
                chunk_size=chunk_size,
                buffer_size=buffer_size,
                target_speed_bps=target_speed,
                max_concurrent_chunks=self._calculate_concurrent_chunks(target_speed),
                socket_timeout=self._calculate_socket_timeout(target_speed)
            )
            
            self._notify_parameters_updated()
            
    def _adjust_parameters(self, current_speed: float):
        """Adjust parameters based on current performance."""
        if not self.recent_speeds:
            return
            
        avg_speed = sum(self.recent_speeds) / len(self.recent_speeds)
        target_speed = self.current_params.target_speed_bps
        
        # Determine if we should ramp up or down
        if self.settings.enable_speed_ramping:
            if avg_speed < target_speed * 0.8 and not self.congestion_detected:
                self._ramp_up()
            elif avg_speed > target_speed * 1.2 or self.congestion_detected:
                self._ramp_down()
                
    def _ramp_up(self):
        """Gradually increase transfer parameters."""
        if not self.settings.enable_speed_ramping:
            return
            
        self.ramping_up = True
        self.ramping_down = False
        
        # Increase chunk size
        new_chunk_size = min(
            int(self.current_params.chunk_size * self.settings.ramp_up_factor),
            self.settings.chunk_size_max
        )
        
        # Increase buffer size
        new_buffer_size = min(
            int(self.current_params.buffer_size * self.settings.ramp_up_factor),
            self.settings.buffer_size_max
        )
        
        self.current_params.chunk_size = new_chunk_size
        self.current_params.buffer_size = new_buffer_size
        
        self._notify_parameters_updated()
        self.logger.debug(f"Ramped up: chunk={new_chunk_size}, buffer={new_buffer_size}")
        
    def _ramp_down(self):
        """Gradually decrease transfer parameters."""
        self.ramping_up = False
        self.ramping_down = True
        
        # Decrease chunk size
        new_chunk_size = max(
            int(self.current_params.chunk_size * self.settings.ramp_down_factor),
            self.settings.chunk_size_min
        )
        
        # Decrease buffer size
        new_buffer_size = max(
            int(self.current_params.buffer_size * self.settings.ramp_down_factor),
            self.settings.buffer_size_min
        )
        
        self.current_params.chunk_size = new_chunk_size
        self.current_params.buffer_size = new_buffer_size
        
        self._notify_parameters_updated()
        self.logger.debug(f"Ramped down: chunk={new_chunk_size}, buffer={new_buffer_size}")
        
    def _calculate_optimal_chunk_size(self, target_speed: float) -> int:
        """Calculate optimal chunk size based on target speed."""
        # Base chunk size on speed - faster connections can handle larger chunks
        if target_speed > 50 * 1024 * 1024:  # > 50 MB/s
            base_size = 512 * 1024  # 512KB
        elif target_speed > 10 * 1024 * 1024:  # > 10 MB/s
            base_size = 256 * 1024  # 256KB
        elif target_speed > 1 * 1024 * 1024:  # > 1 MB/s
            base_size = 128 * 1024  # 128KB
        else:
            base_size = 64 * 1024  # 64KB
            
        return max(self.settings.chunk_size_min, 
                  min(base_size, self.settings.chunk_size_max))
        
    def _calculate_optimal_buffer_size(self, target_speed: float) -> int:
        """Calculate optimal buffer size based on target speed."""
        # Buffer should be large enough for ~100ms of data
        buffer_size = int(target_speed * 0.1)  # 100ms worth
        
        return max(self.settings.buffer_size_min,
                  min(buffer_size, self.settings.buffer_size_max))
        
    def _calculate_concurrent_chunks(self, target_speed: float) -> int:
        """Calculate optimal number of concurrent chunks."""
        # More concurrent chunks for higher speeds
        if target_speed > 20 * 1024 * 1024:  # > 20 MB/s
            return 4
        elif target_speed > 5 * 1024 * 1024:  # > 5 MB/s
            return 2
        else:
            return 1
            
    def _calculate_socket_timeout(self, target_speed: float) -> float:
        """Calculate optimal socket timeout based on target speed."""
        # Faster connections can have shorter timeouts
        if target_speed > 10 * 1024 * 1024:  # > 10 MB/s
            return 5.0
        elif target_speed > 1 * 1024 * 1024:  # > 1 MB/s
            return 10.0
        else:
            return 15.0
            
    def _recalculate_parameters(self):
        """Recalculate parameters based on current settings."""
        available_bandwidth = self.bandwidth_detector.current_metrics.available_bandwidth_bps
        if available_bandwidth > 0:
            self._initialize_parameters(available_bandwidth)
            
    def _on_network_metrics_updated(self, metrics: NetworkMetrics):
        """Handle network metrics updates."""
        with self.lock:
            # Update congestion detection
            old_congestion = self.congestion_detected
            self.congestion_detected = (
                metrics.congestion_detected or 
                metrics.quality in [NetworkQuality.POOR, NetworkQuality.VERY_POOR]
            )
            
            # If congestion state changed, adjust parameters
            if old_congestion != self.congestion_detected:
                if self.congestion_detected:
                    self.logger.info("Network congestion detected - reducing transfer parameters")
                    self._ramp_down()
                else:
                    self.logger.info("Network congestion cleared - allowing parameter increase")
                    
    def _notify_parameters_updated(self):
        """Notify listeners that parameters were updated."""
        if self.on_parameters_updated:
            self.on_parameters_updated(self.current_params)

    def _on_quality_updated(self, metrics: QualityMetrics):
        """Handle network quality updates."""
        # Adjust parameters based on quality
        if metrics.quality_level in [QualityLevel.POOR, QualityLevel.VERY_POOR]:
            if not self.congestion_detected:
                self.logger.info(f"Poor network quality detected: {metrics.quality_level.value}")
                self.congestion_detected = True
                self._ramp_down()
        elif metrics.quality_level in [QualityLevel.EXCELLENT, QualityLevel.GOOD]:
            if self.congestion_detected:
                self.logger.info(f"Network quality improved: {metrics.quality_level.value}")
                self.congestion_detected = False

    def _on_quality_degraded(self, quality_level: QualityLevel):
        """Handle network quality degradation."""
        self.logger.warning(f"Network quality degraded to: {quality_level.value}")
        self.congestion_detected = True
        self._ramp_down()

    def get_network_quality_metrics(self) -> Optional[QualityMetrics]:
        """Get current network quality metrics."""
        return self.quality_monitor.get_current_metrics()

    def get_quality_score(self) -> float:
        """Get current network quality score (0-100)."""
        return self.quality_monitor.get_quality_score()

#!/usr/bin/env python3
"""
Setup script for File Transfer Pro.
"""

import sys
import subprocess
import os
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✓ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True


def install_dependencies():
    """Install required dependencies."""
    print("📦 Installing dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def create_desktop_shortcut():
    """Create desktop shortcut (Windows only)."""
    if sys.platform != "win32":
        return
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "File Transfer Pro.lnk")
        target = os.path.join(os.getcwd(), "main.py")
        wDir = os.getcwd()
        icon = target
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = f'"{target}"'
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = icon
        shortcut.save()
        
        print("✅ Desktop shortcut created")
        
    except ImportError:
        print("ℹ️  Install pywin32 to create desktop shortcuts")
    except Exception as e:
        print(f"⚠️  Could not create desktop shortcut: {e}")


def run_tests():
    """Run basic tests to verify installation."""
    print("🧪 Running tests...")
    
    try:
        # Test imports
        sys.path.insert(0, str(Path(__file__).parent / "src"))
        
        from src.core.server import FileTransferServer
        from src.core.client import FileTransferClient
        from src.gui.modern_main_window import ModernMainWindow
        from src.utils.network_utils import NetworkUtils
        from src.utils.file_utils import FileUtils
        
        print("✅ All imports successful")
        
        # Test basic functionality
        assert NetworkUtils.is_valid_ip("127.0.0.1")
        assert NetworkUtils.is_valid_port(8888)
        assert FileUtils.format_file_size(1024) == "1.0 KB"
        
        print("✅ Basic functionality tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Tests failed: {e}")
        return False


def main():
    """Main setup function."""
    print("🚀 File Transfer Pro - Setup")
    print("=" * 35)
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Install dependencies
    if not install_dependencies():
        return 1
    
    # Run tests
    if not run_tests():
        print("⚠️  Some tests failed, but the application might still work")
    
    # Create desktop shortcut
    create_desktop_shortcut()
    
    print("\n🎉 Setup completed successfully!")
    print("\n📋 How to use:")
    print("  🖥️  GUI Mode:    python main.py")
    print("  📤 Send Mode:   python main.py --mode client")
    print("  📥 Server Mode: python main.py --mode server")
    print("  📚 Help:       python main.py --help")
    
    print("\n💡 Tips:")
    print("  • Use GUI mode for the best experience")
    print("  • Start server mode to receive files")
    print("  • Share your IP address with others to receive files")
    print("  • Check the user guide in docs/user_guide.md")
    
    print("\n🔧 Build executable:")
    print("  python build_executable.py")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())

# File Transfer Application - User Guide

## Overview

The File Transfer Application is a secure, fast, and user-friendly tool for transferring files between computers over a network. It provides an alternative to paid cloud services for sharing large files.

## Features

- **Fast TCP-based transfers** - Reliable file transfer with automatic chunking
- **Intuitive GUI** - Easy-to-use interface with file picker and drag-drop
- **Real-time progress** - Progress bars, transfer speed, and ETA
- **Secure transfers** - Optional AES encryption for file content
- **Multiple files** - Queue and transfer multiple files simultaneously
- **Resume capability** - Resume interrupted transfers
- **Cross-platform** - Works on Windows, Mac, and Linux

## Getting Started

### Installation

1. **Prerequisites**:
   - Python 3.7 or higher
   - pip (Python package installer)

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**:
   ```bash
   python main.py
   ```

### Basic Usage

The application has two main modes:

1. **Send Files** (Client mode) - Send files to another computer
2. **Receive Files** (Server mode) - Receive files from other computers

## Sending Files

1. **Start the application** and go to the "Send Files" tab
2. **Configure connection**:
   - Enter the IP address of the receiving computer
   - Enter the port number (default: 8888)
   - Click "Connect"
3. **Select files**:
   - Click "Add Files" to select individual files
   - Click "Add Folder" to add all files from a folder
   - Use "Clear List" to remove all files
4. **Send files**:
   - Click "Send Files" to start the transfer
   - Monitor progress in the progress bar
   - Wait for completion

## Receiving Files

1. **Start the application** and go to the "Receive Files" tab
2. **Configure server**:
   - Choose listen address (0.0.0.0 for all interfaces, localhost for local only)
   - Set port number (default: 8888)
   - Choose download directory
3. **Start server**:
   - Click "Start Server"
   - Share your IP address and port with the sender
   - Monitor incoming transfers in the activity log
4. **Stop server** when done

## Settings

Access settings through the "Settings" tab:

### Network Settings
- **Chunk Size**: Size of data chunks for transfer (1-1024 KB, recommended: 8-64)
- **Connection Timeout**: How long to wait for connections (1-300 seconds)
- **Max Connections**: Maximum simultaneous connections (1-100)
- **Default Server Settings**: Default host and port for server mode

### Security Settings
- **Enable Encryption**: Encrypt files using AES-256 (reduces speed but increases security)
- **Verify Checksums**: Verify file integrity using MD5 checksums

### General Settings
- **Default Download Directory**: Where received files are saved
- **Auto-start Server**: Automatically start server when application launches

## Network Configuration

### Finding Your IP Address

To receive files, you need to share your IP address with the sender:

1. The application shows your local IP in the status bar
2. For external connections, you may need your public IP address
3. Use online tools like "whatismyip.com" to find your public IP

### Firewall Configuration

You may need to configure your firewall to allow connections:

1. **Windows**: Add an exception for the application or port
2. **Mac**: System Preferences > Security & Privacy > Firewall > Options
3. **Linux**: Use `ufw` or `iptables` to allow the port

### Router Configuration (Port Forwarding)

For transfers over the internet, configure port forwarding on your router:

1. Access your router's admin panel (usually *********** or ***********)
2. Find "Port Forwarding" or "Virtual Server" settings
3. Forward the application port (default: 8888) to your computer's local IP
4. Use your public IP address for external connections

## Troubleshooting

### Connection Issues

**Problem**: Cannot connect to server
- **Solution**: Check IP address and port number
- **Solution**: Verify server is running and listening
- **Solution**: Check firewall settings
- **Solution**: Ensure both computers are on the same network (for local transfers)

**Problem**: Connection times out
- **Solution**: Increase connection timeout in settings
- **Solution**: Check network connectivity
- **Solution**: Verify port forwarding (for internet transfers)

### Transfer Issues

**Problem**: Transfer fails or is very slow
- **Solution**: Reduce chunk size in settings
- **Solution**: Check network stability
- **Solution**: Disable encryption temporarily
- **Solution**: Close other network-intensive applications

**Problem**: File corruption detected
- **Solution**: Enable checksum verification in settings
- **Solution**: Check network quality
- **Solution**: Try smaller chunk sizes

### Application Issues

**Problem**: Application won't start
- **Solution**: Check Python version (3.7+ required)
- **Solution**: Install missing dependencies: `pip install -r requirements.txt`
- **Solution**: Check for error messages in the console

**Problem**: GUI doesn't appear
- **Solution**: Ensure tkinter is installed (usually included with Python)
- **Solution**: Try running with `python main.py --mode gui`

## Command Line Usage

The application also supports command line operation:

```bash
# Start GUI (default)
python main.py

# Start as server
python main.py --mode server --host 0.0.0.0 --port 8888

# Start as client
python main.py --mode client --host ************* --port 8888

# Enable verbose logging
python main.py --verbose

# Log to file
python main.py --log-file transfer.log
```

## Security Considerations

### Encryption
- Enable encryption for sensitive files
- Encryption uses AES-256 with secure key exchange
- Encrypted transfers are slower but more secure

### Network Security
- Use local networks when possible
- Be cautious when opening ports to the internet
- Consider using VPN for secure remote transfers

### File Safety
- Verify checksums for important files
- Scan received files with antivirus software
- Be cautious with executable files from unknown sources

## Performance Tips

### Optimizing Transfer Speed
1. **Adjust chunk size**: Larger chunks for fast networks, smaller for slow/unreliable networks
2. **Disable encryption**: For non-sensitive files on trusted networks
3. **Use wired connections**: Ethernet is faster and more reliable than Wi-Fi
4. **Close other applications**: Reduce network and CPU usage

### Large File Transfers
1. **Increase chunk size**: Use 32-64 KB for large files
2. **Ensure stable connection**: Avoid Wi-Fi for very large files
3. **Monitor progress**: Use the detailed progress dialog
4. **Plan for interruptions**: The application supports resume capability

## Support

For issues, questions, or feature requests:

- Check this user guide first
- Review the troubleshooting section
- Check the application logs for error messages
- Contact: <EMAIL>

## Version History

- **v1.0.0**: Initial release with core functionality
  - TCP-based file transfer
  - GUI with progress tracking
  - Multiple file support
  - Optional encryption
  - Cross-platform compatibility

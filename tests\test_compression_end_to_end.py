"""
End-to-end tests for compression system integration.
"""

import os
import tempfile
import threading
import time
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.core.server import FileTransferServer
from src.core.enhanced_client import EnhancedFileTransferClient
from src.utils.compression import CompressionMethod, FileCompressor
from src.utils.network_utils import NetworkUtils
from src.utils.file_utils import FileUtils
import pytest


class TestCompressionEndToEnd:
    """End-to-end tests for compression system."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for testing."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        
        # Cleanup
        import shutil
        try:
            shutil.rmtree(temp_dir)
        except:
            pass
    
    def test_compression_reduces_transfer_time(self, temp_dir):
        """Test that compression actually reduces transfer time for compressible files."""
        # Create a highly compressible file
        test_file = Path(temp_dir) / "compressible.txt"
        content = "This is highly repetitive content that should compress very well. " * 10000
        test_file.write_text(content)
        
        original_size = FileUtils.get_file_size(str(test_file))
        
        # Test compression effectiveness first
        compressor = FileCompressor()
        result = compressor.compress_file(str(test_file), CompressionMethod.GZIP)
        
        assert result.success
        assert result.compression_ratio < 0.1  # Should compress to less than 10%
        
        compressor.cleanup()
        
        # Find available ports
        port1 = NetworkUtils.find_available_port(start_port=9000)
        port2 = NetworkUtils.find_available_port(start_port=port1 + 1)
        
        # Test with compression enabled
        receive_dir1 = Path(temp_dir) / "received_compressed"
        receive_dir1.mkdir()
        
        transfer_time_compressed = self._transfer_file_and_measure_time(
            str(test_file), str(receive_dir1), port1, enable_compression=True
        )
        
        # Test with compression disabled
        receive_dir2 = Path(temp_dir) / "received_uncompressed"
        receive_dir2.mkdir()
        
        transfer_time_uncompressed = self._transfer_file_and_measure_time(
            str(test_file), str(receive_dir2), port2, enable_compression=False
        )
        
        # Verify both transfers completed successfully
        assert (receive_dir1 / "compressible.txt").exists()
        assert (receive_dir2 / "compressible.txt").exists()
        
        # Verify file integrity
        assert (receive_dir1 / "compressible.txt").read_text() == content
        assert (receive_dir2 / "compressible.txt").read_text() == content
        
        # Compressed transfer should be faster for highly compressible content
        print(f"Compressed transfer time: {transfer_time_compressed:.2f}s")
        print(f"Uncompressed transfer time: {transfer_time_uncompressed:.2f}s")
        print(f"Original file size: {FileUtils.format_file_size(original_size)}")
        
        # For highly compressible content, compression should provide benefit
        # Allow some tolerance for small files and test environment variations
        if original_size > 100000:  # Only check for files > 100KB
            assert transfer_time_compressed < transfer_time_uncompressed * 1.2
    
    def test_compression_with_different_file_types(self, temp_dir):
        """Test compression behavior with different file types."""
        # Create different types of files
        test_files = []
        
        # Text file (highly compressible)
        text_file = Path(temp_dir) / "document.txt"
        text_content = "Lorem ipsum dolor sit amet, consectetur adipiscing elit. " * 1000
        text_file.write_text(text_content)
        test_files.append(("document.txt", str(text_file), True))
        
        # JSON file (compressible)
        json_file = Path(temp_dir) / "data.json"
        json_content = '{"users": [{"name": "John", "age": 30}, {"name": "Jane", "age": 25}]}' * 500
        json_file.write_text(json_content)
        test_files.append(("data.json", str(json_file), True))
        
        # Binary file (less compressible)
        binary_file = Path(temp_dir) / "random.dat"
        import random
        binary_content = bytes(random.randint(0, 255) for _ in range(50000))
        binary_file.write_bytes(binary_content)
        test_files.append(("random.dat", str(binary_file), False))
        
        # Setup server and client
        port = NetworkUtils.find_available_port(start_port=9000)
        receive_dir = Path(temp_dir) / "received"
        receive_dir.mkdir()
        
        server = FileTransferServer(
            host="localhost",
            port=port,
            download_dir=str(receive_dir),
            chunk_size=4096,
            enable_compression=True
        )
        
        received_files = []
        compression_stats = []
        
        def on_file_received(filename, client_id):
            received_files.append(filename)
        
        server.on_file_received = on_file_received
        
        # Start server
        server_thread = threading.Thread(target=server.start, daemon=True)
        server_thread.start()
        time.sleep(0.5)
        
        try:
            # Create client with compression
            client = EnhancedFileTransferClient(
                chunk_size=4096,
                enable_compression=True,
                compression_method=CompressionMethod.AUTO
            )
            
            assert client.connect("localhost", port)
            
            # Send all files and track compression
            for filename, file_path, should_compress_well in test_files:
                original_size = FileUtils.get_file_size(file_path)
                
                success = client.send_file(file_path)
                assert success, f"Failed to send {filename}"
                
                time.sleep(0.5)  # Wait for transfer
                
                # Verify file received
                received_file = receive_dir / filename
                assert received_file.exists(), f"File {filename} not received"
                
                # Verify file integrity
                if filename.endswith('.dat'):
                    assert received_file.read_bytes() == Path(file_path).read_bytes()
                else:
                    assert received_file.read_text() == Path(file_path).read_text()
                
                compression_stats.append({
                    'filename': filename,
                    'original_size': original_size,
                    'should_compress_well': should_compress_well
                })
            
            client.disconnect()
            
        finally:
            server.stop()
        
        # Verify all files were received
        assert len(received_files) == len(test_files)
        
        # Verify compression behavior
        for stat in compression_stats:
            print(f"{stat['filename']}: {FileUtils.format_file_size(stat['original_size'])}")
    
    def test_compression_error_handling(self, temp_dir):
        """Test compression system error handling."""
        # Create a file that might cause compression issues
        test_file = Path(temp_dir) / "test.txt"
        test_file.write_text("Small file")
        
        port = NetworkUtils.find_available_port(start_port=9000)
        receive_dir = Path(temp_dir) / "received"
        receive_dir.mkdir()
        
        # Test with server compression enabled, client disabled
        server = FileTransferServer(
            host="localhost",
            port=port,
            download_dir=str(receive_dir),
            chunk_size=4096,
            enable_compression=True
        )
        
        received_files = []
        
        def on_file_received(filename, client_id):
            received_files.append(filename)
        
        server.on_file_received = on_file_received
        
        server_thread = threading.Thread(target=server.start, daemon=True)
        server_thread.start()
        time.sleep(0.5)
        
        try:
            # Client with compression disabled
            client = EnhancedFileTransferClient(
                chunk_size=4096,
                enable_compression=False
            )
            
            assert client.connect("localhost", port)
            
            # Should still work even with mismatched compression settings
            success = client.send_file(str(test_file))
            assert success
            
            time.sleep(0.5)
            
            # File should be received correctly
            assert len(received_files) == 1
            received_file = receive_dir / "test.txt"
            assert received_file.exists()
            assert received_file.read_text() == "Small file"
            
            client.disconnect()
            
        finally:
            server.stop()
    
    def _transfer_file_and_measure_time(self, file_path: str, receive_dir: str, port: int, enable_compression: bool) -> float:
        """Helper method to transfer a file and measure the time taken."""
        server = FileTransferServer(
            host="localhost",
            port=port,
            download_dir=receive_dir,
            chunk_size=4096,
            enable_compression=enable_compression
        )
        
        transfer_completed = threading.Event()
        
        def on_file_received(filename, client_id):
            transfer_completed.set()
        
        server.on_file_received = on_file_received
        
        # Start server
        server_thread = threading.Thread(target=server.start, daemon=True)
        server_thread.start()
        time.sleep(0.5)
        
        try:
            # Create client
            client = EnhancedFileTransferClient(
                chunk_size=4096,
                enable_compression=enable_compression,
                compression_method=CompressionMethod.AUTO if enable_compression else CompressionMethod.NONE
            )
            
            assert client.connect("localhost", port)
            
            # Measure transfer time
            start_time = time.time()
            success = client.send_file(file_path)
            assert success
            
            # Wait for transfer to complete
            transfer_completed.wait(timeout=30)
            end_time = time.time()
            
            client.disconnect()
            
            return end_time - start_time
            
        finally:
            server.stop()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

"""
Tests for UI color and theme functionality.
"""

import tkinter as tk
from tkinter import ttk
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.gui.theme import ModernTheme
import unittest


class TestUIColors(unittest.TestCase):
    """Test UI color and theme functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.root = tk.Tk()
        self.root.withdraw()  # Hide the window during testing
        
    def tearDown(self):
        """Clean up test fixtures."""
        self.root.destroy()
    
    def test_theme_application(self):
        """Test that theme can be applied without errors."""
        try:
            style = ModernTheme.apply_theme(self.root)
            self.assertIsInstance(style, ttk.Style)
        except Exception as e:
            self.fail(f"Theme application failed: {e}")
    
    def test_color_definitions(self):
        """Test that all required colors are defined."""
        required_colors = [
            'primary', 'primary_hover', 'primary_light',
            'secondary', 'secondary_hover', 'secondary_light',
            'success', 'warning', 'error', 'info',
            'bg_primary', 'bg_secondary', 'bg_tertiary', 'bg_dark',
            'text_primary', 'text_secondary', 'text_muted', 'text_white',
            'border_light', 'border_medium', 'border_dark',
            'accent', 'highlight'
        ]
        
        for color in required_colors:
            self.assertIn(color, ModernTheme.COLORS, f"Color '{color}' not defined")
            self.assertIsInstance(ModernTheme.COLORS[color], str, f"Color '{color}' is not a string")
            self.assertTrue(ModernTheme.COLORS[color].startswith('#'), f"Color '{color}' is not a hex color")
    
    def test_font_definitions(self):
        """Test that all required fonts are defined."""
        required_fonts = ['default', 'heading', 'subheading', 'small', 'monospace']
        
        for font in required_fonts:
            self.assertIn(font, ModernTheme.FONTS, f"Font '{font}' not defined")
            self.assertIsInstance(ModernTheme.FONTS[font], tuple, f"Font '{font}' is not a tuple")
    
    def test_label_styles(self):
        """Test that label styles are properly configured."""
        style = ModernTheme.apply_theme(self.root)

        # Test that styles exist and have proper configuration
        label_styles = [
            'TLabel', 'Heading.TLabel', 'Subheading.TLabel',
            'Muted.TLabel', 'Success.TLabel', 'Error.TLabel',
            'Sidebar.TLabel', 'SidebarMuted.TLabel', 'Status.TLabel', 'Info.TLabel'
        ]

        for style_name in label_styles:
            try:
                config = style.configure(style_name)
                # Style should exist and return configuration
                self.assertIsInstance(config, dict, f"Style '{style_name}' configuration should be a dict")
                # Should have foreground and background colors
                if config:  # Only check if config is not empty
                    self.assertIn('foreground', config, f"Style '{style_name}' should have foreground color")
                    self.assertIn('background', config, f"Style '{style_name}' should have background color")
            except tk.TclError:
                self.fail(f"Style '{style_name}' does not exist")
    
    def test_button_styles(self):
        """Test that button styles are properly configured."""
        style = ModernTheme.apply_theme(self.root)

        button_styles = [
            'TButton', 'Primary.TButton', 'Secondary.TButton',
            'Success.TButton', 'Danger.TButton'
        ]

        for style_name in button_styles:
            try:
                config = style.configure(style_name)
                # Style should exist and return configuration
                self.assertIsInstance(config, dict, f"Style '{style_name}' configuration should be a dict")
            except tk.TclError:
                self.fail(f"Style '{style_name}' does not exist")
    
    def test_text_contrast(self):
        """Test that text colors provide sufficient contrast."""
        # Test primary text on primary background
        text_color = ModernTheme.COLORS['text_primary']
        bg_color = ModernTheme.COLORS['bg_primary']
        
        # Convert hex to RGB for contrast calculation
        def hex_to_rgb(hex_color):
            hex_color = hex_color.lstrip('#')
            return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        
        def calculate_luminance(rgb):
            """Calculate relative luminance of a color."""
            r, g, b = [x / 255.0 for x in rgb]
            
            def gamma_correct(c):
                return c / 12.92 if c <= 0.03928 else ((c + 0.055) / 1.055) ** 2.4
            
            r, g, b = map(gamma_correct, [r, g, b])
            return 0.2126 * r + 0.7152 * g + 0.0722 * b
        
        def contrast_ratio(color1, color2):
            """Calculate contrast ratio between two colors."""
            lum1 = calculate_luminance(hex_to_rgb(color1))
            lum2 = calculate_luminance(hex_to_rgb(color2))
            
            lighter = max(lum1, lum2)
            darker = min(lum1, lum2)
            
            return (lighter + 0.05) / (darker + 0.05)
        
        # Test primary text contrast
        primary_contrast = contrast_ratio(text_color, bg_color)
        self.assertGreater(primary_contrast, 4.5, 
                          f"Primary text contrast ratio {primary_contrast:.2f} is too low (should be > 4.5)")
        
        # Test muted text contrast
        muted_text_color = ModernTheme.COLORS['text_muted']
        muted_contrast = contrast_ratio(muted_text_color, bg_color)
        self.assertGreater(muted_contrast, 3.0, 
                          f"Muted text contrast ratio {muted_contrast:.2f} is too low (should be > 3.0)")
    
    def test_widget_creation_with_styles(self):
        """Test that widgets can be created with theme styles."""
        style = ModernTheme.apply_theme(self.root)
        
        # Create a test frame
        test_frame = ttk.Frame(self.root)
        
        # Test creating widgets with different styles
        widgets_to_test = [
            (ttk.Label, {'style': 'Heading.TLabel', 'text': 'Test Heading'}),
            (ttk.Label, {'style': 'Muted.TLabel', 'text': 'Test Muted'}),
            (ttk.Button, {'style': 'Primary.TButton', 'text': 'Test Button'}),
            (ttk.Entry, {'style': 'TEntry'}),
        ]
        
        for widget_class, kwargs in widgets_to_test:
            try:
                widget = widget_class(test_frame, **kwargs)
                self.assertIsNotNone(widget)
            except Exception as e:
                self.fail(f"Failed to create {widget_class.__name__} with style {kwargs.get('style', 'default')}: {e}")
    
    def test_color_accessibility(self):
        """Test that colors meet basic accessibility guidelines."""
        colors = ModernTheme.COLORS
        
        # Test that error color is distinguishable from success color
        error_rgb = tuple(int(colors['error'].lstrip('#')[i:i+2], 16) for i in (0, 2, 4))
        success_rgb = tuple(int(colors['success'].lstrip('#')[i:i+2], 16) for i in (0, 2, 4))
        
        # Colors should be significantly different
        color_distance = sum((a - b) ** 2 for a, b in zip(error_rgb, success_rgb)) ** 0.5
        self.assertGreater(color_distance, 100, "Error and success colors are too similar")
        
        # Test that primary and secondary colors are different
        primary_rgb = tuple(int(colors['primary'].lstrip('#')[i:i+2], 16) for i in (0, 2, 4))
        secondary_rgb = tuple(int(colors['secondary'].lstrip('#')[i:i+2], 16) for i in (0, 2, 4))
        
        primary_secondary_distance = sum((a - b) ** 2 for a, b in zip(primary_rgb, secondary_rgb)) ** 0.5
        self.assertGreater(primary_secondary_distance, 50, "Primary and secondary colors are too similar")


class TestUIIntegration(unittest.TestCase):
    """Integration tests for UI components."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.root = tk.Tk()
        self.root.withdraw()  # Hide the window during testing
        
    def tearDown(self):
        """Clean up test fixtures."""
        self.root.destroy()
    
    def test_settings_dialog_colors(self):
        """Test that settings dialog uses proper colors."""
        from src.gui.settings_dialog import SettingsDialog
        
        try:
            # Create settings dialog
            dialog = SettingsDialog(self.root)
            
            # Dialog should be created without errors
            self.assertIsNotNone(dialog.dialog)
            
            # Close the dialog
            dialog.dialog.destroy()
            
        except Exception as e:
            self.fail(f"Settings dialog creation failed: {e}")
    
    def test_progress_dialog_colors(self):
        """Test that progress dialog uses proper colors."""
        from src.gui.progress_dialog import ProgressDialog
        
        try:
            # Create progress dialog
            dialog = ProgressDialog(self.root, "Test Progress")
            
            # Dialog should be created without errors
            self.assertIsNotNone(dialog.dialog)
            
            # Close the dialog
            dialog.dialog.destroy()
            
        except Exception as e:
            self.fail(f"Progress dialog creation failed: {e}")


if __name__ == '__main__':
    unittest.main()

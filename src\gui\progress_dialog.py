"""
Progress dialog for file transfers.
"""

import tkinter as tk
from tkinter import ttk
import time
from typing import Optional, Callable
from src.utils.file_utils import FileUtils


class ProgressDialog:
    """
    Dialog window showing transfer progress with detailed information.
    """
    
    def __init__(self, parent: tk.Tk, title: str = "Transfer Progress"):
        """
        Initialize the progress dialog.
        
        Args:
            parent: Parent window
            title: Dialog title
        """
        self.parent = parent
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x300")
        self.dialog.resizable(False, False)
        
        # Make dialog modal
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center dialog
        self._center_dialog()
        
        # Transfer state
        self.start_time = time.time()
        self.bytes_transferred = 0
        self.total_bytes = 0
        self.current_file = ""
        self.is_cancelled = False
        
        # Callbacks
        self.on_cancel: Optional[Callable] = None
        
        # Create widgets
        self._create_widgets()
        
        # Handle dialog close
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_close)
    
    def _create_widgets(self):
        """Create and layout dialog widgets."""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Current file label
        self.file_label = ttk.Label(main_frame, text="Preparing transfer...", style='Subheading.TLabel')
        self.file_label.pack(pady=(0, 10))
        
        # Overall progress
        ttk.Label(main_frame, text="Overall Progress:").pack(anchor=tk.W)
        self.overall_progress = ttk.Progressbar(main_frame, mode='determinate', length=400)
        self.overall_progress.pack(pady=(5, 10), fill=tk.X)
        
        # Current file progress
        ttk.Label(main_frame, text="Current File:").pack(anchor=tk.W)
        self.file_progress = ttk.Progressbar(main_frame, mode='determinate', length=400)
        self.file_progress.pack(pady=(5, 15), fill=tk.X)
        
        # Statistics frame
        stats_frame = ttk.LabelFrame(main_frame, text="Transfer Statistics", padding="10")
        stats_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Create statistics labels
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill=tk.X)
        
        # Configure grid columns
        stats_grid.columnconfigure(1, weight=1)
        stats_grid.columnconfigure(3, weight=1)
        
        # Row 1: Speed and ETA
        ttk.Label(stats_grid, text="Speed:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.speed_label = ttk.Label(stats_grid, text="0 B/s")
        self.speed_label.grid(row=0, column=1, sticky=tk.W)
        
        ttk.Label(stats_grid, text="ETA:").grid(row=0, column=2, sticky=tk.W, padx=(20, 5))
        self.eta_label = ttk.Label(stats_grid, text="--:--")
        self.eta_label.grid(row=0, column=3, sticky=tk.W)
        
        # Row 2: Transferred and Total
        ttk.Label(stats_grid, text="Transferred:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(5, 0))
        self.transferred_label = ttk.Label(stats_grid, text="0 B")
        self.transferred_label.grid(row=1, column=1, sticky=tk.W, pady=(5, 0))
        
        ttk.Label(stats_grid, text="Total:").grid(row=1, column=2, sticky=tk.W, padx=(20, 5), pady=(5, 0))
        self.total_label = ttk.Label(stats_grid, text="0 B")
        self.total_label.grid(row=1, column=3, sticky=tk.W, pady=(5, 0))
        
        # Row 3: Elapsed time and Files
        ttk.Label(stats_grid, text="Elapsed:").grid(row=2, column=0, sticky=tk.W, padx=(0, 5), pady=(5, 0))
        self.elapsed_label = ttk.Label(stats_grid, text="00:00")
        self.elapsed_label.grid(row=2, column=1, sticky=tk.W, pady=(5, 0))
        
        ttk.Label(stats_grid, text="Files:").grid(row=2, column=2, sticky=tk.W, padx=(20, 5), pady=(5, 0))
        self.files_label = ttk.Label(stats_grid, text="0 / 0")
        self.files_label.grid(row=2, column=3, sticky=tk.W, pady=(5, 0))
        
        # Buttons frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        # Cancel button
        self.cancel_btn = ttk.Button(button_frame, text="Cancel", command=self._on_cancel)
        self.cancel_btn.pack(side=tk.RIGHT)
        
        # Close button (initially hidden)
        self.close_btn = ttk.Button(button_frame, text="Close", command=self._on_close)
        
    def _center_dialog(self):
        """Center the dialog on the parent window."""
        self.dialog.update_idletasks()
        
        # Get parent window position and size
        parent_x = self.parent.winfo_x()
        parent_y = self.parent.winfo_y()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        # Get dialog size
        dialog_width = self.dialog.winfo_width()
        dialog_height = self.dialog.winfo_height()
        
        # Calculate center position
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
    
    def update_progress(
        self,
        current_file: str,
        file_progress: float,
        overall_progress: float,
        bytes_transferred: int,
        total_bytes: int,
        current_file_index: int = 0,
        total_files: int = 1
    ):
        """
        Update the progress dialog with current transfer information.
        
        Args:
            current_file: Name of the current file being transferred
            file_progress: Progress of current file (0-100)
            overall_progress: Overall transfer progress (0-100)
            bytes_transferred: Total bytes transferred
            total_bytes: Total bytes to transfer
            current_file_index: Index of current file (0-based)
            total_files: Total number of files
        """
        self.current_file = current_file
        self.bytes_transferred = bytes_transferred
        self.total_bytes = total_bytes
        
        # Update file label
        self.file_label.config(text=f"Transferring: {current_file}")
        
        # Update progress bars
        self.file_progress.config(value=file_progress)
        self.overall_progress.config(value=overall_progress)
        
        # Update statistics
        self._update_statistics(current_file_index + 1, total_files)
        
        # Update dialog
        self.dialog.update()
    
    def _update_statistics(self, current_file_num: int, total_files: int):
        """Update the statistics display."""
        current_time = time.time()
        elapsed_time = current_time - self.start_time
        
        # Calculate speed
        if elapsed_time > 0:
            speed_bps = self.bytes_transferred / elapsed_time
            speed_text = f"{FileUtils.format_file_size(int(speed_bps))}/s"
        else:
            speed_text = "0 B/s"
        
        # Calculate ETA
        if self.bytes_transferred > 0 and self.total_bytes > 0:
            remaining_bytes = self.total_bytes - self.bytes_transferred
            if speed_bps > 0:
                eta_seconds = remaining_bytes / speed_bps
                eta_text = self._format_time(eta_seconds)
            else:
                eta_text = "--:--"
        else:
            eta_text = "--:--"
        
        # Update labels
        self.speed_label.config(text=speed_text)
        self.eta_label.config(text=eta_text)
        self.transferred_label.config(text=FileUtils.format_file_size(self.bytes_transferred))
        self.total_label.config(text=FileUtils.format_file_size(self.total_bytes))
        self.elapsed_label.config(text=self._format_time(elapsed_time))
        self.files_label.config(text=f"{current_file_num} / {total_files}")
    
    def _format_time(self, seconds: float) -> str:
        """
        Format time in seconds to MM:SS or HH:MM:SS format.
        
        Args:
            seconds: Time in seconds
            
        Returns:
            Formatted time string
        """
        if seconds < 0:
            return "--:--"
        
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{secs:02d}"
        else:
            return f"{minutes:02d}:{secs:02d}"
    
    def set_completed(self, success: bool = True):
        """
        Mark the transfer as completed.
        
        Args:
            success: Whether the transfer was successful
        """
        if success:
            self.file_label.config(text="Transfer completed successfully!")
            self.overall_progress.config(value=100)
            self.file_progress.config(value=100)
        else:
            self.file_label.config(text="Transfer failed or was cancelled.")
        
        # Hide cancel button and show close button
        self.cancel_btn.pack_forget()
        self.close_btn.pack(side=tk.RIGHT)
    
    def _on_cancel(self):
        """Handle cancel button click."""
        self.is_cancelled = True
        if self.on_cancel:
            self.on_cancel()
        self.set_completed(success=False)
    
    def _on_close(self):
        """Handle dialog close."""
        self.dialog.destroy()
    
    def show(self):
        """Show the dialog."""
        self.dialog.deiconify()
    
    def hide(self):
        """Hide the dialog."""
        self.dialog.withdraw()
    
    def destroy(self):
        """Destroy the dialog."""
        self.dialog.destroy()

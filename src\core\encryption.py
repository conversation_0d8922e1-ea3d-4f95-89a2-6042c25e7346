"""
Encryption utilities for secure file transfer.
"""

import os
import hashlib
from typing import <PERSON><PERSON>, Optional
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.backends import default_backend
from src.utils.logger import get_logger


class EncryptionManager:
    """
    Manages encryption and decryption for file transfers.
    """
    
    def __init__(self):
        """Initialize the encryption manager."""
        self.logger = get_logger()
        self.backend = default_backend()
        
        # Generate RSA key pair for key exchange
        self.private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
            backend=self.backend
        )
        self.public_key = self.private_key.public_key()
    
    def get_public_key_pem(self) -> bytes:
        """
        Get the public key in PEM format.
        
        Returns:
            Public key in PEM format
        """
        return self.public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
    
    def load_public_key_pem(self, pem_data: bytes) -> bool:
        """
        Load a public key from PEM data.
        
        Args:
            pem_data: Public key in PEM format
            
        Returns:
            True if loaded successfully, False otherwise
        """
        try:
            self.peer_public_key = serialization.load_pem_public_key(
                pem_data,
                backend=self.backend
            )
            return True
        except Exception as e:
            self.logger.error(f"Failed to load public key: {e}")
            return False
    
    def generate_aes_key(self) -> bytes:
        """
        Generate a random AES-256 key.
        
        Returns:
            32-byte AES key
        """
        return os.urandom(32)
    
    def encrypt_aes_key(self, aes_key: bytes, public_key_pem: bytes) -> Optional[bytes]:
        """
        Encrypt AES key using RSA public key.
        
        Args:
            aes_key: AES key to encrypt
            public_key_pem: RSA public key in PEM format
            
        Returns:
            Encrypted AES key or None if failed
        """
        try:
            public_key = serialization.load_pem_public_key(
                public_key_pem,
                backend=self.backend
            )
            
            encrypted_key = public_key.encrypt(
                aes_key,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            
            return encrypted_key
            
        except Exception as e:
            self.logger.error(f"Failed to encrypt AES key: {e}")
            return None
    
    def decrypt_aes_key(self, encrypted_key: bytes) -> Optional[bytes]:
        """
        Decrypt AES key using RSA private key.
        
        Args:
            encrypted_key: Encrypted AES key
            
        Returns:
            Decrypted AES key or None if failed
        """
        try:
            aes_key = self.private_key.decrypt(
                encrypted_key,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            
            return aes_key
            
        except Exception as e:
            self.logger.error(f"Failed to decrypt AES key: {e}")
            return None
    
    def encrypt_data(self, data: bytes, key: bytes) -> Tuple[bytes, bytes]:
        """
        Encrypt data using AES-256-GCM.
        
        Args:
            data: Data to encrypt
            key: AES encryption key
            
        Returns:
            Tuple of (encrypted_data, nonce)
        """
        # Generate random nonce
        nonce = os.urandom(12)  # 96-bit nonce for GCM
        
        # Create cipher
        cipher = Cipher(
            algorithms.AES(key),
            modes.GCM(nonce),
            backend=self.backend
        )
        
        encryptor = cipher.encryptor()
        encrypted_data = encryptor.update(data) + encryptor.finalize()
        
        # Return encrypted data with authentication tag
        return encrypted_data + encryptor.tag, nonce
    
    def decrypt_data(self, encrypted_data: bytes, key: bytes, nonce: bytes) -> Optional[bytes]:
        """
        Decrypt data using AES-256-GCM.
        
        Args:
            encrypted_data: Encrypted data (including authentication tag)
            key: AES decryption key
            nonce: Nonce used for encryption
            
        Returns:
            Decrypted data or None if failed
        """
        try:
            # Split encrypted data and authentication tag
            ciphertext = encrypted_data[:-16]  # All but last 16 bytes
            tag = encrypted_data[-16:]  # Last 16 bytes
            
            # Create cipher
            cipher = Cipher(
                algorithms.AES(key),
                modes.GCM(nonce, tag),
                backend=self.backend
            )
            
            decryptor = cipher.decryptor()
            decrypted_data = decryptor.update(ciphertext) + decryptor.finalize()
            
            return decrypted_data
            
        except Exception as e:
            self.logger.error(f"Failed to decrypt data: {e}")
            return None
    
    def derive_key_from_password(self, password: str, salt: bytes) -> bytes:
        """
        Derive encryption key from password using PBKDF2.
        
        Args:
            password: Password string
            salt: Random salt bytes
            
        Returns:
            Derived key
        """
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,  # 256-bit key
            salt=salt,
            iterations=100000,  # OWASP recommended minimum
            backend=self.backend
        )
        
        return kdf.derive(password.encode('utf-8'))
    
    def generate_salt(self) -> bytes:
        """
        Generate random salt for key derivation.
        
        Returns:
            16-byte random salt
        """
        return os.urandom(16)
    
    def calculate_file_hash(self, file_path: str) -> str:
        """
        Calculate SHA-256 hash of a file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Hexadecimal hash string
        """
        hash_sha256 = hashlib.sha256()
        
        try:
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            
            return hash_sha256.hexdigest()
            
        except Exception as e:
            self.logger.error(f"Failed to calculate file hash: {e}")
            return ""
    
    def verify_file_integrity(self, file_path: str, expected_hash: str) -> bool:
        """
        Verify file integrity using hash comparison.
        
        Args:
            file_path: Path to the file
            expected_hash: Expected hash value
            
        Returns:
            True if file is intact, False otherwise
        """
        actual_hash = self.calculate_file_hash(file_path)
        return actual_hash.lower() == expected_hash.lower()

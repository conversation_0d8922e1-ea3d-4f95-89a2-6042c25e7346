"""
Enhanced error handling and validation system.
"""

import logging
import traceback
import functools
from typing import Any, Callable, Optional, Dict, List
from enum import Enum
from dataclasses import dataclass
from src.utils.logger import get_logger


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories."""
    NETWORK = "network"
    FILE_SYSTEM = "file_system"
    VALIDATION = "validation"
    SECURITY = "security"
    SYSTEM = "system"
    USER_INPUT = "user_input"


@dataclass
class ErrorInfo:
    """Detailed error information."""
    code: str
    message: str
    category: ErrorCategory
    severity: ErrorSeverity
    details: Optional[Dict[str, Any]] = None
    suggestions: Optional[List[str]] = None


class ErrorHandler:
    """
    Centralized error handling and validation system.
    """
    
    # Predefined error codes and messages
    ERROR_CODES = {
        # Network errors
        "NET_001": ErrorInfo(
            "NET_001", "Connection refused", ErrorCategory.NETWORK, ErrorSeverity.HIGH,
            suggestions=["Check if the server is running", "Verify the host and port", "Check firewall settings"]
        ),
        "NET_002": ErrorInfo(
            "NET_002", "Connection timeout", ErrorCategory.NETWORK, ErrorSeverity.MEDIUM,
            suggestions=["Check network connectivity", "Increase timeout value", "Try a different server"]
        ),
        "NET_003": ErrorInfo(
            "NET_003", "Invalid host address", ErrorCategory.VALIDATION, ErrorSeverity.MEDIUM,
            suggestions=["Enter a valid IP address or hostname", "Check DNS resolution"]
        ),
        "NET_004": ErrorInfo(
            "NET_004", "Invalid port number", ErrorCategory.VALIDATION, ErrorSeverity.MEDIUM,
            suggestions=["Enter a port number between 1 and 65535"]
        ),
        "NET_005": ErrorInfo(
            "NET_005", "Port already in use", ErrorCategory.NETWORK, ErrorSeverity.HIGH,
            suggestions=["Choose a different port", "Stop the application using this port"]
        ),
        
        # File system errors
        "FS_001": ErrorInfo(
            "FS_001", "File not found", ErrorCategory.FILE_SYSTEM, ErrorSeverity.HIGH,
            suggestions=["Check if the file exists", "Verify the file path"]
        ),
        "FS_002": ErrorInfo(
            "FS_002", "Permission denied", ErrorCategory.FILE_SYSTEM, ErrorSeverity.HIGH,
            suggestions=["Check file permissions", "Run as administrator if needed"]
        ),
        "FS_003": ErrorInfo(
            "FS_003", "Insufficient disk space", ErrorCategory.FILE_SYSTEM, ErrorSeverity.CRITICAL,
            suggestions=["Free up disk space", "Choose a different destination"]
        ),
        "FS_004": ErrorInfo(
            "FS_004", "Invalid file path", ErrorCategory.VALIDATION, ErrorSeverity.MEDIUM,
            suggestions=["Enter a valid file path", "Avoid special characters"]
        ),
        "FS_005": ErrorInfo(
            "FS_005", "File corruption detected", ErrorCategory.FILE_SYSTEM, ErrorSeverity.HIGH,
            suggestions=["Retry the transfer", "Check source file integrity"]
        ),
        
        # Security errors
        "SEC_001": ErrorInfo(
            "SEC_001", "Encryption failed", ErrorCategory.SECURITY, ErrorSeverity.HIGH,
            suggestions=["Check encryption settings", "Verify key exchange"]
        ),
        "SEC_002": ErrorInfo(
            "SEC_002", "Checksum verification failed", ErrorCategory.SECURITY, ErrorSeverity.HIGH,
            suggestions=["Retry the transfer", "Check network stability"]
        ),
        
        # System errors
        "SYS_001": ErrorInfo(
            "SYS_001", "Out of memory", ErrorCategory.SYSTEM, ErrorSeverity.CRITICAL,
            suggestions=["Close other applications", "Reduce file size", "Restart the application"]
        ),
        "SYS_002": ErrorInfo(
            "SYS_002", "System resource unavailable", ErrorCategory.SYSTEM, ErrorSeverity.HIGH,
            suggestions=["Wait and retry", "Restart the application"]
        ),
        
        # User input errors
        "UI_001": ErrorInfo(
            "UI_001", "Required field is empty", ErrorCategory.USER_INPUT, ErrorSeverity.MEDIUM,
            suggestions=["Fill in all required fields"]
        ),
        "UI_002": ErrorInfo(
            "UI_002", "Invalid input format", ErrorCategory.USER_INPUT, ErrorSeverity.MEDIUM,
            suggestions=["Check input format", "Follow the expected pattern"]
        ),
    }
    
    def __init__(self):
        """Initialize the error handler."""
        self.logger = get_logger()
        self.error_history: List[Dict[str, Any]] = []
    
    def handle_error(self, error_code: str, exception: Optional[Exception] = None, 
                    context: Optional[Dict[str, Any]] = None) -> ErrorInfo:
        """
        Handle an error with the given code.
        
        Args:
            error_code: Error code
            exception: Optional exception object
            context: Additional context information
            
        Returns:
            ErrorInfo object with error details
        """
        error_info = self.ERROR_CODES.get(error_code)
        if not error_info:
            error_info = ErrorInfo(
                error_code, "Unknown error", ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM
            )
        
        # Log the error
        log_message = f"[{error_code}] {error_info.message}"
        if context:
            log_message += f" - Context: {context}"
        
        if error_info.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(log_message, exc_info=exception)
        elif error_info.severity == ErrorSeverity.HIGH:
            self.logger.error(log_message, exc_info=exception)
        elif error_info.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(log_message)
        else:
            self.logger.info(log_message)
        
        # Add to error history
        error_entry = {
            "code": error_code,
            "message": error_info.message,
            "category": error_info.category.value,
            "severity": error_info.severity.value,
            "timestamp": self._get_timestamp(),
            "context": context,
            "exception": str(exception) if exception else None
        }
        self.error_history.append(error_entry)
        
        # Keep only last 100 errors
        if len(self.error_history) > 100:
            self.error_history = self.error_history[-100:]
        
        return error_info
    
    def get_error_suggestions(self, error_code: str) -> List[str]:
        """Get suggestions for resolving an error."""
        error_info = self.ERROR_CODES.get(error_code)
        return error_info.suggestions if error_info and error_info.suggestions else []
    
    def get_error_history(self, category: Optional[ErrorCategory] = None, 
                         severity: Optional[ErrorSeverity] = None) -> List[Dict[str, Any]]:
        """
        Get error history with optional filtering.
        
        Args:
            category: Filter by error category
            severity: Filter by error severity
            
        Returns:
            List of error entries
        """
        history = self.error_history
        
        if category:
            history = [e for e in history if e["category"] == category.value]
        
        if severity:
            history = [e for e in history if e["severity"] == severity.value]
        
        return history
    
    def _get_timestamp(self) -> str:
        """Get current timestamp."""
        import datetime
        return datetime.datetime.now().isoformat()


class ValidationError(Exception):
    """Custom validation error."""
    
    def __init__(self, error_code: str, message: str = None):
        self.error_code = error_code
        super().__init__(message or error_code)


class Validator:
    """
    Input validation utilities.
    """
    
    @staticmethod
    def validate_host(host: str) -> bool:
        """Validate host address (IP or hostname)."""
        if not host or not host.strip():
            return False
        
        host = host.strip()
        
        # Check if it's a valid IP address
        from src.utils.network_utils import NetworkUtils
        if NetworkUtils.is_valid_ip(host):
            return True
        
        # Check if it's a valid hostname
        import re
        hostname_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
        return bool(re.match(hostname_pattern, host)) and len(host) <= 253
    
    @staticmethod
    def validate_port(port: Any) -> bool:
        """Validate port number."""
        try:
            port_num = int(port)
            return 1 <= port_num <= 65535
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def validate_file_path(file_path: str) -> bool:
        """Validate file path."""
        if not file_path or not file_path.strip():
            return False
        
        try:
            from pathlib import Path
            path = Path(file_path)
            return path.exists() and path.is_file()
        except Exception:
            return False
    
    @staticmethod
    def validate_directory_path(dir_path: str) -> bool:
        """Validate directory path."""
        if not dir_path or not dir_path.strip():
            return False
        
        try:
            from pathlib import Path
            path = Path(dir_path)
            return path.exists() and path.is_dir()
        except Exception:
            return False
    
    @staticmethod
    def validate_file_size(file_path: str, max_size: int = None) -> bool:
        """Validate file size."""
        try:
            from src.utils.file_utils import FileUtils
            size = FileUtils.get_file_size(file_path)
            
            if size == 0:
                return False
            
            if max_size and size > max_size:
                return False
            
            return True
        except Exception:
            return False


def error_handler(error_code: str = None, reraise: bool = False):
    """
    Decorator for automatic error handling.
    
    Args:
        error_code: Default error code to use
        reraise: Whether to reraise the exception after handling
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                handler = ErrorHandler()
                code = error_code or "SYS_002"
                handler.handle_error(code, e, {"function": func.__name__})
                
                if reraise:
                    raise
                
                return None
        return wrapper
    return decorator


def validate_input(validator_func: Callable, error_code: str):
    """
    Decorator for input validation.
    
    Args:
        validator_func: Function to validate input
        error_code: Error code to raise if validation fails
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Validate first argument (assuming it's the input to validate)
            if args and not validator_func(args[0]):
                raise ValidationError(error_code)
            
            return func(*args, **kwargs)
        return wrapper
    return decorator


# Global error handler instance
global_error_handler = ErrorHandler()

#!/usr/bin/env python3
"""
Speed Optimization Demo - Showcases the dynamic transfer speed optimization system.
"""

import sys
import time
import tempfile
import threading
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.server import FileTransferServer
from src.core.resilient_client import ResilientFileTransferClient
from src.utils.network_utils import NetworkUtils
from src.utils.file_utils import FileUtils
from src.utils.bandwidth_detector import BandwidthDetector
from src.utils.speed_controller import AdaptiveSpeedController, SpeedSettings, SpeedMode
from src.utils.network_quality_monitor import NetworkQualityMonitor
from src.utils.logger import setup_logger


def create_test_file(file_path: Path, size_mb: int):
    """Create a test file of specified size."""
    print(f"📄 Creating test file: {file_path.name} ({size_mb} MB)")
    
    with open(file_path, 'wb') as f:
        # Write in 1MB chunks
        chunk = b'A' * (1024 * 1024)
        for _ in range(size_mb):
            f.write(chunk)
    
    print(f"   ✅ Created {FileUtils.format_file_size(file_path.stat().st_size)}")


def demonstrate_bandwidth_detection():
    """Demonstrate bandwidth detection capabilities."""
    print("\n🌐 BANDWIDTH DETECTION DEMO")
    print("=" * 50)
    
    detector = BandwidthDetector()
    
    print("🔍 Detecting available bandwidth...")
    start_time = time.time()
    
    # Detect bandwidth
    bandwidth = detector.detect_available_bandwidth()
    
    detection_time = time.time() - start_time
    
    print(f"   📊 Detected bandwidth: {FileUtils.format_transfer_speed(bandwidth)}")
    print(f"   📊 In Mbps: {FileUtils.format_speed_mbps(bandwidth)}")
    print(f"   ⏱️  Detection time: {detection_time:.2f} seconds")
    
    # Start monitoring
    print("\n🔄 Starting bandwidth monitoring...")
    detector.start_monitoring()
    
    # Monitor for a few seconds
    time.sleep(5)
    
    metrics = detector.get_current_metrics()
    print(f"   📈 Current metrics:")
    print(f"      • Speed: {FileUtils.format_transfer_speed(metrics.current_speed_bps)}")
    print(f"      • Quality: {metrics.quality.value}")
    print(f"      • Latency: {metrics.latency_ms:.1f}ms")
    
    detector.stop_monitoring()
    return bandwidth


def demonstrate_speed_controller(available_bandwidth: float):
    """Demonstrate adaptive speed controller."""
    print("\n⚡ SPEED CONTROLLER DEMO")
    print("=" * 50)
    
    # Create bandwidth detector and speed controller
    detector = BandwidthDetector()
    controller = AdaptiveSpeedController(detector)
    
    # Test different speed modes
    modes = [SpeedMode.CONSERVATIVE, SpeedMode.BALANCED, SpeedMode.AGGRESSIVE]
    
    for mode in modes:
        print(f"\n🎛️  Testing {mode.value.upper()} mode:")
        
        settings = SpeedSettings(
            mode=mode,
            max_speed_bps=None,  # No limit
            enable_congestion_control=True,
            enable_speed_ramping=True
        )
        
        controller.configure(settings)
        
        # Mock available bandwidth
        controller._initialize_parameters(available_bandwidth)
        
        params = controller.get_current_parameters()
        
        print(f"   📦 Chunk size: {FileUtils.format_file_size(params.chunk_size)}")
        print(f"   🗂️  Buffer size: {FileUtils.format_file_size(params.buffer_size)}")
        print(f"   🎯 Target speed: {FileUtils.format_transfer_speed(params.target_speed_bps)}")
        print(f"   🔗 Max concurrent: {params.max_concurrent_chunks}")
        print(f"   ⏰ Socket timeout: {params.socket_timeout:.1f}s")
    
    # Test throttling
    print(f"\n🚦 Testing speed throttling:")
    
    # Set speed limit to 5 MB/s
    settings = SpeedSettings(max_speed_bps=5 * 1024 * 1024)
    controller.configure(settings)
    
    test_speeds = [1*1024*1024, 3*1024*1024, 7*1024*1024, 10*1024*1024]  # 1, 3, 7, 10 MB/s
    
    for speed in test_speeds:
        should_throttle = controller.should_throttle(speed)
        delay = controller.calculate_throttle_delay(speed, 64*1024)  # 64KB chunk
        
        print(f"   📊 {FileUtils.format_transfer_speed(speed)}: "
              f"{'🔴 THROTTLE' if should_throttle else '🟢 ALLOW'} "
              f"(delay: {delay:.3f}s)")


def demonstrate_quality_monitoring():
    """Demonstrate network quality monitoring."""
    print("\n📡 NETWORK QUALITY MONITORING DEMO")
    print("=" * 50)
    
    monitor = NetworkQualityMonitor()
    
    print("🔍 Measuring network quality...")
    
    # Measure quality
    metrics = monitor.measure_quality()
    
    print(f"   📊 Quality metrics:")
    print(f"      • Latency: {metrics.latency_ms:.1f}ms")
    print(f"      • Jitter: {metrics.jitter_ms:.1f}ms")
    print(f"      • Packet loss: {metrics.packet_loss_percent:.1f}%")
    print(f"      • Quality level: {metrics.quality_level.value.upper()}")
    
    # Get quality score
    monitor.current_metrics = metrics
    score = monitor.get_quality_score()
    print(f"      • Quality score: {score:.1f}/100")
    
    # Start monitoring
    print("\n🔄 Starting quality monitoring...")
    
    quality_updates = []
    
    def on_quality_updated(metrics):
        quality_updates.append(metrics)
        print(f"   📈 Quality update: {metrics.quality_level.value} "
              f"(latency: {metrics.latency_ms:.1f}ms)")
    
    monitor.on_quality_updated = on_quality_updated
    monitor.start_monitoring()
    
    # Monitor for a few seconds
    time.sleep(10)
    
    monitor.stop_monitoring()
    
    print(f"   📊 Collected {len(quality_updates)} quality updates")


def demonstrate_optimized_transfer():
    """Demonstrate optimized file transfer."""
    print("\n🚀 OPTIMIZED TRANSFER DEMO")
    print("=" * 50)
    
    # Create temporary directories
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        send_path = temp_path / "send"
        receive_path = temp_path / "receive"
        send_path.mkdir()
        receive_path.mkdir()
        
        # Create test file
        test_file = send_path / "speed_test.dat"
        create_test_file(test_file, 50)  # 50 MB file
        
        print(f"\n📊 Test file: {FileUtils.format_file_size(test_file.stat().st_size)}")
        
        # Find available port
        port = NetworkUtils.find_available_port(start_port=9000)
        print(f"🌐 Using port: {port}")
        
        # Setup server
        print("\n🖥️  Setting up optimized server...")
        server = FileTransferServer(
            host="localhost",
            port=port,
            download_dir=str(receive_path),
            chunk_size=64*1024,  # Will be optimized dynamically
            enable_compression=False  # Disable for pure speed test
        )
        
        # Track transfer events
        transfer_stats = {
            'start_time': None,
            'end_time': None,
            'bytes_received': 0,
            'speed_samples': []
        }
        
        def on_file_received(filename, client_id):
            transfer_stats['end_time'] = time.time()
            received_file = receive_path / filename
            transfer_stats['bytes_received'] = received_file.stat().st_size
            print(f"   ✅ File received: {filename}")
        
        server.on_file_received = on_file_received
        
        # Start server
        server_thread = threading.Thread(target=server.start, daemon=True)
        server_thread.start()
        time.sleep(1)  # Let server start
        
        # Setup optimized client
        print("📱 Setting up optimized client...")
        client = ResilientFileTransferClient()
        
        # Configure speed optimization
        speed_settings = {
            "enable_speed_optimization": True,
            "max_speed_mbps": 0,  # No limit
            "speed_mode": "aggressive",
            "enable_congestion_control": True,
            "enable_speed_ramping": True,
            "auto_detect_bandwidth": True,
            "chunk_size_min_kb": 8,
            "chunk_size_max_kb": 1024,
            "buffer_size_min_kb": 64,
            "buffer_size_max_kb": 4096
        }
        
        client.configure_speed_optimization(speed_settings)
        
        # Track transfer progress
        def on_progress(filename, progress, bytes_sent, total_bytes, speed):
            transfer_stats['speed_samples'].append(speed)
            if len(transfer_stats['speed_samples']) % 10 == 0:  # Every 10th update
                print(f"   📊 Progress: {progress:.1f}% - "
                      f"Speed: {FileUtils.format_transfer_speed(speed)} "
                      f"({FileUtils.format_speed_mbps(speed)})")
        
        client.on_transfer_progress = on_progress
        
        # Connect and transfer
        print(f"\n🔗 Connecting to localhost:{port}...")
        if client.connect("localhost", port):
            print("   ✅ Connected successfully")
            
            transfer_stats['start_time'] = time.time()
            
            print(f"\n📤 Starting optimized transfer of {test_file.name}...")
            success = client.send_file(str(test_file))
            
            if success:
                # Calculate statistics
                duration = transfer_stats['end_time'] - transfer_stats['start_time']
                avg_speed = transfer_stats['bytes_received'] / duration
                max_speed = max(transfer_stats['speed_samples']) if transfer_stats['speed_samples'] else 0
                
                print(f"\n📊 TRANSFER STATISTICS:")
                print(f"   📁 File size: {FileUtils.format_file_size(transfer_stats['bytes_received'])}")
                print(f"   ⏱️  Duration: {duration:.2f} seconds")
                print(f"   📈 Average speed: {FileUtils.format_transfer_speed(avg_speed)}")
                print(f"   📈 Average speed (Mbps): {FileUtils.format_speed_mbps(avg_speed)}")
                print(f"   🚀 Peak speed: {FileUtils.format_transfer_speed(max_speed)}")
                print(f"   🚀 Peak speed (Mbps): {FileUtils.format_speed_mbps(max_speed)}")
                print(f"   📊 Speed samples: {len(transfer_stats['speed_samples'])}")
                
                # Calculate efficiency
                if hasattr(client, 'available_bandwidth') and client.available_bandwidth > 0:
                    efficiency = (avg_speed / client.available_bandwidth) * 100
                    print(f"   ⚡ Network utilization: {efficiency:.1f}%")
                
            else:
                print("   ❌ Transfer failed")
                
            client.disconnect()
        else:
            print("   ❌ Connection failed")
        
        # Stop server
        server.stop()


def main():
    """Main demo function."""
    print("🚀 DYNAMIC SPEED OPTIMIZATION DEMO")
    print("=" * 60)
    print("This demo showcases the advanced speed optimization features")
    print("of the file transfer application.")
    print()
    
    # Setup logging
    setup_logger(level="INFO")
    
    try:
        # 1. Bandwidth Detection
        available_bandwidth = demonstrate_bandwidth_detection()
        
        # 2. Speed Controller
        demonstrate_speed_controller(available_bandwidth)
        
        # 3. Quality Monitoring
        demonstrate_quality_monitoring()
        
        # 4. Optimized Transfer
        demonstrate_optimized_transfer()
        
        print("\n🎉 DEMO COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print("The speed optimization system provides:")
        print("• 🔍 Automatic bandwidth detection")
        print("• ⚡ Dynamic speed adaptation")
        print("• 🚦 Intelligent throttling")
        print("• 📡 Network quality monitoring")
        print("• 🎛️  Multiple optimization modes")
        print("• 📊 Real-time performance metrics")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
